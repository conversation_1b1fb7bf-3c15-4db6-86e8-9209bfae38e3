package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.VectorFieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.List;

public record Multiplication(

        @NonNull
        @NotEmpty
        List<Formula> operands

) implements Formula, NaryFunction, NumericNaryFunction {

    @Override
    public @NonNull NumberFieldValue compute(@NonNull @NotEmpty VectorFieldValue<BigDecimal> operandVector) {
        BigDecimal value = BigDecimal.ONE;
        for (ScalarFieldValue<BigDecimal> operandValue : operandVector.values()) {
            value = value.multiply(operandValue.value());
        }
        return NumberFieldValue.of(value);
    }

    /**
     * Presnost nasobeni je suma presnosti vsech operandu
     */
    @Override
    public @NonNull ScalarDatatype resolveDatatype(@NonNull @NotEmpty List<ScalarDatatype> operandDatatypes) {
        return DatatypeUtil.getSumPrecisionDatatype(operandDatatypes);
    }

    @Override
    public String toString() {
        return "Multiply(%s)".formatted(StringUtil.listToString(operands, ", "));
    }
}
