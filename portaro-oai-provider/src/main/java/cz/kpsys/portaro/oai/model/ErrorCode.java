package cz.kpsys.portaro.oai.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public enum ErrorCode {

    @JsonProperty("cannotDisseminateFormat")
    CANNOT_DISSEMINATE_FORMAT,

    @JsonProperty("idDoesNotExist")
    ID_DOES_NOT_EXIST,

    @JsonProperty("badArgument")
    BAD_ARGUMENT,

    @JsonProperty("badVerb")
    BAD_VERB,

    @JsonProperty("noMetadataFormats")
    NO_METADATA_FORMATS,

    @JsonProperty("noRecordsMatch")
    NO_RECORDS_MATCH,

    @JsonProperty("badResumptionToken")
    BAD_RESUMPTION_TOKEN,

    @JsonProperty("noSetHierarchy")
    NO_SET_HIERARCHY,

    @JsonProperty("unknownError")
    UNKNOWN;

}
