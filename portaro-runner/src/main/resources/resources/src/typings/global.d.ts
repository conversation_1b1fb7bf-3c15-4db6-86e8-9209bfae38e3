/// <reference types="svelte" />

// `$$Generic` is used by svelte and svelte language-tools (svelte-check, etc.) to mark type as generic type argument
// more info:
// https://github.com/dummdidumm/rfcs/blob/ts-typedefs-within-svelte-components/text/ts-typing-props-slots-events.md
// https://github.com/sveltejs/language-tools/issues/442
// declaration just to prevent IDEA to show errors of using undeclared type `$$Generic`
// eslint-disable-next-line @typescript-eslint/no-empty-object-type,@typescript-eslint/no-unused-vars
declare interface $$Generic<EXTENDED_TYPE = never> {}

// to allow for custom element attributes and events
// https://github.com/sveltejs/language-tools/blob/master/docs/preprocessors/typescript.md#im-using-an-attributeevent-on-a-dom-element-and-it-throws-a-type-error
declare namespace svelteHTML {
    interface HTMLAttributes {
        'data-id'?: any;
        'data-name'?: any;
        'data-main-input'?: any;
    }
}