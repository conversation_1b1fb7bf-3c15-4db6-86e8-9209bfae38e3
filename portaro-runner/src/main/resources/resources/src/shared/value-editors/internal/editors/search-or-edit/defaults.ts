import type {DefaultOptions} from '../../../types';
import type {SearchOrEditValueEditorLocalizations, SearchOrEditValueEditorOptions} from './types';
import type {SvelteComponentConstructor} from 'core/types';
import DefaultSearchOrEditModelComponent from './templates/DefaultSearchOrEditModelComponent.svelte';


/**
 * @ngdoc constant
 * @name SEARCH_OR_EDIT_VALUE_EDITOR_DEFAULT_OPTIONS
 * @module portaro.value-editors.search-or-edit
 *
 * @description
 * For description see {@link SearchOrEditValueEditorOptions}
 *
 * ```javascript
 * {
 *      modelComponent: DefaultSearchOrEditModelComponent,
 *      additionalParameters: undefined,
 *      searchModelFunction: undefined,
 *      editModelFunction: undefined,
 *      immediatelyTriggerSearch: false,
 *      allowToDeleteValue: false
 * }
 * ```
 */
export const SEARCH_OR_EDIT_VALUE_EDITOR_DEFAULT_OPTIONS: DefaultOptions<SearchOrEditValueEditorOptions> = {
    modelComponent: DefaultSearchOrEditModelComponent as SvelteComponentConstructor<{value}>,
    searchParams: undefined,
    createParams: undefined,
    editParams: undefined,
    searchModelFunction: undefined,
    editModelFunction: undefined,
    immediatelyTriggerSearch: false,
    allowToDeleteValue: false
};

/**
 * @ngdoc constant
 * @name SEARCH_OR_EDIT_VALUE_EDITOR_DEFAULT_LOCALIZATIONS
 * @module portaro.value-editors.search-or-edit
 *
 * @description
 * ```
 * {
 *      search: 'Search',
 *      searchOther: 'Search other',
 *      editValue: 'Edit value',
 *      createNew: 'Create new',
 *      delete: 'Delete'
 * }
 * ```
 */
export const SEARCH_OR_EDIT_VALUE_EDITOR_DEFAULT_LOCALIZATIONS = Object.freeze<SearchOrEditValueEditorLocalizations>({
    search: 'Search',
    searchOther: 'Search other',
    editValue: 'Edit value',
    createNew: 'Create new',
    delete: 'Delete',
    description: '',
});