import valueEditorsModule from 'shared/value-editors/value-editors.base.module';
import type { ValueEditorAliasesService } from './value-editor-aliases.service';

describe('aliases service', () => {

    const ALIAS_NAME = 'some-editor';


    it('should add new alias', () => {
        let _valueEditorAliasesService: ValueEditorAliasesService;

        angular.mock.module(valueEditorsModule);

        inject(/*@ngInject*/ (valueEditorAliasesService: ValueEditorAliasesService) => {
            valueEditorAliasesService.addAlias(ALIAS_NAME, 'text');
            _valueEditorAliasesService = valueEditorAliasesService;
        })

        expect(_valueEditorAliasesService.isAlias(ALIAS_NAME)).toBe(true);
        expect(_valueEditorAliasesService.getAlias(ALIAS_NAME)).toBe('text');
    });

    it('should throw error if alias exist', () => {
        let _valueEditorAliasesService: ValueEditorAliasesService;

        angular.mock.module(valueEditorsModule);

        inject(/*@ngInject*/ (valueEditorAliasesService: ValueEditorAliasesService) => {
            valueEditorAliasesService.addAlias(ALIAS_NAME, 'text');
            _valueEditorAliasesService = valueEditorAliasesService;
        })

        expect(() => _valueEditorAliasesService.addAlias(ALIAS_NAME, 'text')).toThrow();
    });

    it('should remove alias', () => {
        angular.mock.module(valueEditorsModule);

        inject(/*@ngInject*/ (valueEditorAliasesService: ValueEditorAliasesService) => {
            valueEditorAliasesService.addAlias(ALIAS_NAME, 'text');

            expect(() => valueEditorAliasesService.addAlias(ALIAS_NAME, 'text')).toThrow();

            valueEditorAliasesService.removeAlias(ALIAS_NAME);
            expect(valueEditorAliasesService.isAlias(ALIAS_NAME)).toBeFalse();

            expect(() => valueEditorAliasesService.addAlias(ALIAS_NAME, 'text')).not.toThrow();
        })
    });

    it('should throw error if alias does not exist', () => {
        let _kpValueEditorAliasesService: ValueEditorAliasesService;

        angular.mock.module(valueEditorsModule);

        inject(/*@ngInject*/ (valueEditorAliasesService: ValueEditorAliasesService) => {
            valueEditorAliasesService.addAlias(ALIAS_NAME, 'text');
            _kpValueEditorAliasesService = valueEditorAliasesService;
        })

        expect(_kpValueEditorAliasesService.isAlias('sjkkdbcsj')).toBe(false);
        expect(() => _kpValueEditorAliasesService.getAlias('dnwkejbfrj')).toThrow();
    });
});
