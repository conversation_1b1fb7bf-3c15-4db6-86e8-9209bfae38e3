import type {CustomValueEditorLocalizations, ValueEditorType, ValueEditorTypeMap, ValueEditorTypeOrAlias} from '../types';
import type { TestContext} from '../../../test-utils/utils';
import {render} from '../../../test-utils/utils';
import type {Unsubscriber} from 'svelte/store';
import type {Unsubscribable} from 'rxjs';
import type {FormController} from '../internal/forms/types';
import type {SvelteComponent} from 'svelte';
import KpValueEditor from '../kp-value-editor/KpValueEditor.svelte';
import type {ValueEditorBindings} from '../kp-value-editor/types';
import type {ValueEditorsEventMap} from '../events';
import {tick} from 'svelte';
import {firstValueFrom, skip} from 'rxjs';
import type {Field} from 'node_modules/svelte-forms/types';


export interface KpValueEditorProps<MODEL, OPTIONS, VALIDATIONS> extends ValueEditorBindings<OPTIONS, VALIDATIONS> {
    model: MODEL
    getFormController: () => FormController<MODEL>;
}

export async function createEditor<TYPE extends ValueEditorType, MODEL>(
    type: ValueEditorTypeOrAlias,
    context: Map<string, any>,
    model?: MODEL,
    otherProps?: Omit<ValueEditorBindings<ValueEditorTypeMap[TYPE]['options'], ValueEditorTypeMap[TYPE]['validations'], CustomValueEditorLocalizations<ValueEditorTypeMap[TYPE]['localizations']>>, 'type'>,
    appendToBody = false): Promise<TestContext<KpValueEditorProps<MODEL, ValueEditorTypeMap[TYPE]['options'], ValueEditorTypeMap[TYPE]['validations']>>> {
    const result = render<any>(KpValueEditor, {props: {type, model, ...otherProps}, context}, {appendToBody});
    await tick();
    return result;
}

export function getInputElement<T extends Element = HTMLElement>(container: HTMLElement, additionalQuery?: string): T {
    if (!!additionalQuery) {
        container = container.querySelector(additionalQuery);
    }

    return container.querySelector<T>('[data-main-input]');
}

export function getDescriptionElement<T extends Element = HTMLElement>(container: HTMLElement): T {
    return container.querySelector<T>('.editor-description');
}

export function getErrorMessageElementByErrorType<T extends Element = HTMLElement>(container: HTMLElement, errorType: string): T {
    return container.querySelector<T>(`.error-messages .error-message[data-error='${errorType}']`);
}

export function setInputValueOn(input: HTMLInputElement | HTMLTextAreaElement, value: string, eventType: 'input' | 'blur' | 'change' = 'input') {
    input.value = value;
    switch (eventType) {
        case 'input':
            triggerInputEventOn(input);
            break;
        case 'blur':
            triggerBlurEventOn(input);
            break;
        case 'change':
            triggerChangeEvent(input);
            break;
        default:
            throw new Error('unknown input event type');
    }
}

export async function setEditorModelValue<T>(componentInstance: SvelteComponent<T>, model: T extends KpValueEditorProps<infer MODEL, any, any> ? MODEL : never) {
    componentInstance.$$set({model});
    await tick();
    await tick();
}

export async function setEditorProps<T>(componentInstance: SvelteComponent<T>, props: T extends KpValueEditorProps<any, infer OPTIONS, infer VALIDATIONS> ? Omit<ValueEditorBindings<OPTIONS, VALIDATIONS>, 'type'> : never) {
    componentInstance.$$set(props);
    await tick();
}

export function waitForNextState<T>(componentInstance: SvelteComponent<T>, skips = 1): Promise<Field<T extends KpValueEditorProps<infer MODEL, any, any> ? MODEL : never>> {
    return firstValueFrom(componentInstance.getFormController().getFieldState$().pipe(skip(skips)));
}

export function waitForCurrentState<T>(componentInstance: SvelteComponent<T>): Promise<Field<T extends KpValueEditorProps<infer MODEL, any, any> ? MODEL : never>> {
    return firstValueFrom(componentInstance.getFormController().getFieldState$());
}

export function triggerInputEventOn(inputElement: HTMLElement): void {
    const event = new Event('input', {
        bubbles: true,
        cancelable: false,
    });
    inputElement.dispatchEvent(event);
}

export function triggerChangeEvent(inputElement: HTMLElement): void {
    const event = new Event('change', {
        bubbles: true,
        cancelable: false,
    });
    inputElement.dispatchEvent(event);
}

export function triggerBlurEventOn(inputElement: HTMLElement): void {
    const event = new Event('blur', {
        bubbles: false,
        cancelable: false,
    });
    inputElement.dispatchEvent(event);
}

export function triggerFocusEventOn(inputElement: HTMLElement): void {
    const event = new Event('focus', {
        bubbles: false,
        cancelable: false,
    });
    inputElement.dispatchEvent(event);
}

export function triggerFocusoutEventOn(inputElement: HTMLElement): void {
    const event = new Event('focusout', {
        bubbles: true,
        cancelable: false,
    });
    inputElement.dispatchEvent(event);
}

export function triggerMouseupEventOn(inputElement: HTMLElement): void {
    const event = new Event('mouseup', {
        bubbles: true,
        cancelable: false,
    });
    inputElement.dispatchEvent(event);
}

export function cleanup(...args: (Unsubscribable | Unsubscriber | (() => void))[]) {
    args.forEach((item) => {
        if (typeof item === 'function') {
            item();
        }else if (typeof item.unsubscribe === 'function') {
            item.unsubscribe();
        }
    })
}

export function waitForEvent<KEY extends keyof ValueEditorsEventMap<MODEL>, MODEL = any>(component: SvelteComponent, eventName: KEY): Promise<(ValueEditorsEventMap<MODEL>[KEY])> {
    return new Promise((resolve) => {
        const removeListener = component.$on(eventName, (event: CustomEvent<ValueEditorsEventMap<MODEL>[KEY]>) => {resolve(event.detail); removeListener()})
    })
}

export function sleep(timeInMillis: number = 0): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, timeInMillis));
}