import type {AnyObject} from 'typings/portaro.fe.types';
import type {AsyncProcessStatus, UUID} from 'typings/portaro.be.types';
import type {DebouncedFunc, DebounceSettings} from 'lodash-es';
import {Subscription} from 'rxjs';
import {isEqual, memoize, debounce} from 'lodash-es';

/**
 * @module custom-utils
 * @name identifiedValuesEquals
 * @kind function
 *
 * @param {object} iv1 Identified value 1
 * @param {object} iv2 Identified value 2
 *
 * @return {boolean}
 *
 * @description
 * Compares two identified values
 */
export function identifiedValuesEquals(iv1, iv2) {
    if (isUndefined(iv1) || iv1 === null || isUndefined(iv2) || iv2 === null) {
        return false;
    }

    if (iv1 === iv2) {
        return true;
    }

    let val1 = iv1;
    if (isDefined(iv1.id)) {
        val1 = iv1.id;
    }

    let val2 = iv2;
    if (isDefined(iv2.id)) {
        val2 = iv2.id;
    }

    return val1 === val2;
}

/**
 * @module custom-utils
 * @name hash
 * @kind function
 *
 * @param {string} s input
 * @returns {string} computed hash
 *
 * @description
 * Compute simple hash
 */
export function hash(s: string) {
    /* Simple hash function. */
    let a = 1;
    let c = 0;
    let h: number;
    let o: number;

    if (s) {
        a = 0;
        /* eslint-disable no-bitwise */
        for (h = s.length - 1; h >= 0; h--) {
            o = s.charCodeAt(h);
            a = (a << 6 & 268435455) + o + (o << 14);
            c = a & 266338304;
            a = c !== 0 ? a ^ c >> 21 : a;
        }
        /* eslint-enable no-bitwise */
    }
    return String(a);
}

export interface Thenable<T> {
    then: (...args: any[]) => T;
}

/**
 * @module custom-utils
 * @name isThenable
 * @kind function
 *
 * @param value Tested value
 * @returns {boolean} True -> Is thenable object (= have function `then`)
 *
 * @description
 * Checks if value is thenable
 */
export function isThenable<T>(value): value is Thenable<T> {
    return value && typeof value.then === 'function';
}

/**
 * @module custom-utils
 * @name isPromise
 * @kind function
 *
 * @param value Tested value
 * @returns {boolean} True -> Is Promise
 *
 * @description
 * Checks if value is Promise (= has `then` and `catch` functions).
 */
export function isPromise<T>(value): value is Promise<T> {
    return value &&
        typeof value.then === 'function' &&
        typeof value.catch === 'function';
}

/**
 * @module custom-utils
 * @name deepFreeze
 * @kind function
 *
 * @template T
 *
 * @param {T} object Object to freeze
 * @returns {T} Frozen object
 *
 * @description
 * Performs deep freeze on given object
 */
export function deepFreeze<T extends Record<string, any>>(object: T): T {
    Object.freeze(object);

    Object.getOwnPropertyNames(object).forEach((prop) => {
        if (Object.hasOwn(object, prop)
            && object[prop] !== null
            && (typeof object[prop] === 'object' || typeof object[prop] === 'function')
            && !Object.isFrozen(object[prop])) {

            deepFreeze(object[prop]);
        }
    });

    return object;
}

/**
 * @module custom-utils
 * @name uuid
 * @kind function
 *
 * @returns {UUID} UUID
 *
 * @description
 * Returns UUID v4
 */
export function uuid(): UUID {
    // !!! crypto.randomUUID is available only in secure context !!!
    if (typeof crypto.randomUUID === 'function') {
        return crypto.randomUUID() as UUID;
    } else {
        return pseudoRandomUuid(); // fallback for unit testing
    }
}

/**
 * @module custom-utils
 * @name isValidUuidString
 * @kind function
 *
 * @param {string} value tested value
 * @returns {boolean} true if value is valid UUID string
 */
export function isValidUuidString(value: string): boolean {
    if (isNullOrUndefined(value)) {
        return false;
    }
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/;
    return uuidRegex.test(value);
}

function pseudoRandomUuid(): UUID {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        // eslint-disable-next-line
        const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    }) as UUID;
}

/**
 * @module custom-utils
 * @name isFunction
 * @kind function
 *
 * @param functionToCheck
 *
 * @returns {functionToCheck is (...args: any[]) => any}
 *
 * @description
 * Checks if argument is function.
 */
export function isFunction(functionToCheck): functionToCheck is (...args: any[]) => any {
    return functionToCheck && {}.toString.call(functionToCheck) === '[object Function]';
}

/**
 * @module custom-utils
 * @name isEmptyObject
 * @kind function
 *
 * @param {object} value
 *
 * @returns {boolean} true if is empty
 *
 * @description
 * Checks if object is equal to {} (has no properties)
 */
export function isEmptyObject(value: Record<string, any>): boolean {
    return Object.keys(value).length === 0 // no props
        && value.constructor === Object; // for primitive values wrapper classes (new Number())
}

/**
 * @module custom-utils
 * @param {{}} object
 * @param {string[]} keys
 * @returns {{}}
 * @description
 * Filter object keys
 */
export function filterObjectKeys(object: AnyObject, ...keys: string[]): AnyObject {
    return Object.keys(object)
        .filter((key) => keys.includes(key))
        .reduce((res, key) => {
            res[key] = object[key];
            return res;
        }, {});
}

export function isDefined(value: unknown): boolean {
    return typeof value !== 'undefined';
}

export function isNull(value: unknown): value is null {
    return value === null;
}

export function isNullOrUndefined(value: unknown): value is null | undefined {
    return isNull(value) || isUndefined(value);
}

export function isUndefined(value: unknown): value is undefined {
    return typeof value === 'undefined';
}

export function exists(value: unknown): boolean {
    return !isNull(value) && !isUndefined(value);
}

export function assertExists(value: unknown): asserts value is NonNullable<unknown> {
    if (isNullOrUndefined(value)) {
        throw new TypeError('Value is nullable');
    }
}

// https://dev.to/ddiprose/exhaustive-switch-statement-with-typescript-26dh
export function assertUnreachable(value: never, errorMessage?: string): never {
    throw new Error(errorMessage ?? `ERROR! [Exhaustive switch] Reached forbidden guard function with unexpected value: ${JSON.stringify(value)}`);
}

export function isAsyncProcessStopped(asyncProcessStatus: AsyncProcessStatus<any>) {
    return ['FAILED', 'CANCELLED', 'FINISHED'].includes(asyncProcessStatus.state);
}

export function isAsyncProcessRunning(asyncProcessStatus: AsyncProcessStatus<any>) {
    return asyncProcessStatus.state === 'RUNNING';
}

export function hasAsyncProcessFailed(asyncProcessStatus: AsyncProcessStatus<any>) {
    return asyncProcessStatus.state === 'FAILED';
}

export function wasAsyncProcessCancelled(asyncProcessStatus: AsyncProcessStatus<any>) {
    return asyncProcessStatus.state === 'CANCELLED';
}

export function hasAsyncProcessFinishedSuccessfully(asyncProcessStatus: AsyncProcessStatus<any>) {
    return asyncProcessStatus.state === 'FINISHED';
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function ignoreUnusedProperties(...args: any[]) {
    // do nothing - this is just to silence warnings from eslint, svelte-check and others
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function ignoreUnusedPropertiesAsync(...args: any[]): Promise<void> {
    // do nothing - this is just to silence warnings from eslint, svelte-check and others
    return Promise.resolve();
}

export function isTouchDevice(): boolean {
    return matchMedia('(hover: none)').matches;
}

export function isAppleDevice(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform?.toLowerCase() || '';

    // Check for iOS devices: iPhone, iPad, iPod
    const isIos = /iphone|ipad|ipod/.test(userAgent) ||
        /iphone|ipad|ipod/.test(platform) ||
        // Check for iPad on iOS 13+ which reports as Mac
        (platform === 'macintel' && navigator.maxTouchPoints > 1);

    // Check for Mac devices
    const isMac = /mac/.test(platform) || /macintosh/.test(userAgent);

    return isIos || isMac;
}

export function isIosDevice(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform?.toLowerCase() || '';

    // Check for iOS devices: iPhone, iPad, iPod
    return /iphone|ipad|ipod/.test(userAgent) ||
        /iphone|ipad|ipod/.test(platform) ||
        // Check for iPad on iOS 13+ which reports as Mac
        (platform === 'macintel' && navigator.maxTouchPoints > 1);
}

export function isAndroidDevice(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent);
}

export function diffDictionaryObjects<T extends Record<string, any>>(obj1: Partial<T> | null, obj2: Partial<T> | null): Partial<T> {
    const diff: Partial<T> = {};
    const normalisedObj1: Partial<T> = obj1 ?? {};
    const normalisedObj2: Partial<T> = obj2 ?? {};
    const keys: Set<keyof T> = new Set([...Object.keys(normalisedObj1), ...Object.keys(normalisedObj2)]);

    keys.forEach((key) => {
        if (!isEqual(normalisedObj1[key], normalisedObj2[key])) {
            diff[key] = normalisedObj2[key];
        }
    });
    return diff;
}

export function assertTrue(expression: boolean, message = 'Failed assert, expression is false'): void {
    if (!expression) {
        throw new Error(message);
    }
}

type CleanupCapableResource = Subscription | AbortController | (() => void) | undefined;

export function cleanup(...resources: CleanupCapableResource[]) {
    resources.forEach((resource) => {
        if (isNullOrUndefined(resource)) {
            return;
        }
        if (resource instanceof Subscription) {
            resource.unsubscribe();
        }
        if (resource instanceof AbortController) {
            resource.abort();
        }
        if (isFunction(resource)) {
            resource();
        }
    });
}

type AnyFunction = (...args: any[]) => any;

export interface MemoizeDebouncedFunction<F extends AnyFunction>
    extends DebouncedFunc<F> {
    (...args: Parameters<F>): ReturnType<F> | undefined;

    flush: (...args: Parameters<F>) => ReturnType<F> | undefined;
    cancel: (...args: Parameters<F>) => void;
}

/**
 * Combines Lodash's debounce with memoize to allow for debouncing
 * based on parameters passed to the function during runtime.
 * https://docs.actuallycolab.org/engineering-blog/memoize-debounce/
 *
 * @param func The function to debounce.
 * @param wait The number of milliseconds to delay.
 * @param options Lodash debounce options object.
 * @param resolver The function to resolve the cache key.
 */
export function memoizeDebounce<F extends AnyFunction>(
    func: F,
    wait = 0,
    options: DebounceSettings = {},
    resolver?: (...args: Parameters<F>) => unknown
): MemoizeDebouncedFunction<F> {
    const debounceMemo = memoize<(...args: Parameters<F>) => DebouncedFunc<F>>(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        (..._args: Parameters<F>) => debounce(func, wait, options),
        resolver
    );

    function wrappedFunction(
        this: MemoizeDebouncedFunction<F>,
        ...args: Parameters<F>
    ): ReturnType<F> | undefined {
        return debounceMemo(...args)(...args);
    }

    const flush: MemoizeDebouncedFunction<F>['flush'] = (...args) => {
        return debounceMemo(...args).flush();
    };

    const cancel: MemoizeDebouncedFunction<F>['cancel'] = (...args) => {
        return debounceMemo(...args).cancel();
    };

    wrappedFunction.flush = flush;
    wrappedFunction.cancel = cancel;

    return wrappedFunction;
}