<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {RegistrationOptionsModalModel} from './types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import RegistrationOptionItem from './RegistrationOptionItem.svelte';

    export let model: RegistrationOptionsModalModel;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
</script>

<KpModalContent {modalWindowActions} additionalClasses="registration-options-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization registrace.registrace */ 'registrace.registrace')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <ul class="registration-options-list">
            <RegistrationOptionItem {modalWindowActions}
                                    registrationOptions="{model.registrationOptions}"
                                    buttonIcon="lock"
                                    dataQa="registration-option-first-login-registration-button"
                                    option="credentialsRegistration"
                                    buttonLabel="{localize(/* @kp-localization registrace.FirstLogin */ 'registrace.FirstLogin')}"
                                    description="{localize(/* @kp-localization registrace.ZvolteProRegistraciWebovehoPristupu */ 'registrace.ZvolteProRegistraciWebovehoPristupu')}"/>

            <RegistrationOptionItem {modalWindowActions}
                                    registrationOptions="{model.registrationOptions}"
                                    buttonIcon="user"
                                    dataQa="registration-option-full-registration-button"
                                    option="fullRegistration"
                                    buttonLabel="{localize(/* @kp-localization registrace.Predregistrace */ 'registrace.Predregistrace')}"
                                    description="{localize(/* @kp-localization registrace.ZvolteProNovouRegistraci */ 'registrace.ZvolteProNovouRegistraci')}"/>

            <RegistrationOptionItem {modalWindowActions}
                                    registrationOptions="{model.registrationOptions}"
                                    buttonIcon="user"
                                    dataQa="registration-option-my-id-registration-button"
                                    option="mojeIdFullRegistration"
                                    buttonLabel="{localize(/* @kp-localization registrace.PredregistracePomociMojeId */ 'registrace.PredregistracePomociMojeId')}"
                                    description="{localize(/* @kp-localization registrace.ZvolteProNovouRegistraciPomociMojeId */ 'registrace.ZvolteProNovouRegistraciPomociMojeId')}"/>

            <RegistrationOptionItem {modalWindowActions}
                                    registrationOptions="{model.registrationOptions}"
                                    buttonIcon="building-circle-arrow-right"
                                    dataQa="registration-option-mvs-library-registration-button"
                                    option="mvsLibraryRegistration"
                                    buttonLabel="{localize(/* @kp-localization registrace.MvsKnihovna */ 'registrace.MvsKnihovna')}"
                                    description="{localize(/* @kp-localization registrace.ZvolteProRegistraciMvsKnihovny */ 'registrace.ZvolteProRegistraciMvsKnihovny')}"/>
        </ul>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .registration-options-list {
        display: flex;
        flex-direction: column;
        gap: @spacing-l;
    }
</style>