<script lang="ts">
    import type {SelectableCatalogue} from './types';
    import {pipe} from 'core/utils';
    import {getLocalization} from 'core/svelte-context/context';
    import locFilter from 'shared/filters/loc';
    import {ignoreUnusedProperties} from 'shared/utils/custom-utils';

    export let option: SelectableCatalogue;
    export let usedAsLabel = false;

    ignoreUnusedProperties(usedAsLabel);

    const localize = getLocalization();
</script>

{localize(/* @kp-localization login.selectDepartment.GoTo */ 'login.selectDepartment.GoTo')}
{pipe(option.department, locFilter())}
<small>({option.url})</small>