<script lang="ts">
    import type {UserAccountReactivePageData, UserAccountStaticPageData} from '../types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {getPageContext} from 'shared/layouts/page-context';
    import {isReaderBlocked, isReadersRegistrationExpired, isRegistrationNonExisting} from 'shared/utils/user-utils';
    import {UserRoles} from 'shared/services/current-auth.service';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import KpDefinedActionButton from 'shared/components/kp-defined-action-button/KpDefinedActionButton.svelte';
    import WarningBanner from 'shared/ui-widgets/banner/WarningBanner.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import KpUserBarcodeCard from 'src/features/user/kp-user-barcode-card/KpUserBarcodeCard.svelte';

    const localize = getLocalization();
    const pageContext = getPageContext<UserAccountStaticPageData, UserAccountReactivePageData>();

    const user = pageContext.staticData.user;
    const detailView = pageContext.staticData.detailView;
    const readerRole = user.readerAccounts[0];
</script>

<div class="kp-user-account-heading-part">
    <div class="heading-and-card-container">
        <Flex direction="column" gap="sm">
            <div class="heading-row">
                <KpHeading type="h1" data-qa="user-account-name">{pipe(user, loc())}</KpHeading>

                {#if exists(readerRole?.readerCategory)}
                    <span class="reader-readercategory-subheader text-muted">{pipe(readerRole.readerCategory, loc())}</span>
                {/if}
            </div>

            <div class="heading-row email-row">
                {#if user.email}
                    <span class="text-muted">{user.email}</span>
                {/if}

                {#if user.username}
                    <KpChipTag additionalClasses="username-chip-tag">
                        {user.username}
                    </KpChipTag>
                {/if}
            </div>

            <div class="roles-tags-container">
                {#if exists(user.deletionEventId)}
                    <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-deleted">
                        {localize(/* @kp-localization commons.Deleted */ 'commons.Deleted')}
                    </KpChipTag>
                {/if}

                {#if isReaderBlocked(user)}
                    <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-blocked">
                        {localize(/* @kp-localization ctenar.TransactionsBlocked */ 'ctenar.TransactionsBlocked')}
                    </KpChipTag>
                {/if}

                {#if isReadersRegistrationExpired(user)}
                    <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-registration-expired">
                        {localize(/* @kp-localization ctenar.RegistrationExpired */ 'ctenar.RegistrationExpired')}
                    </KpChipTag>
                {/if}

                {#if isRegistrationNonExisting(user)}
                    <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-registration-incomplete">
                        {localize(/* @kp-localization ctenar.RegistrationIncomplete */ 'ctenar.RegistrationIncomplete')}
                    </KpChipTag>
                {/if}

                {#if user.role.includes(UserRoles.ROLE_READER)}
                    <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-reader">
                        {localize(/* @kp-localization user.Reader */ 'user.Reader')}
                    </KpChipTag>
                {/if}

                {#if user.role.includes(UserRoles.ROLE_LIBRARIAN)}
                    <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-librarian">
                        {localize(/* @kp-localization user.Librarian */ 'user.Librarian')}
                    </KpChipTag>
                {/if}

                {#if user.role.includes(UserRoles.ROLE_LIBRARY)}
                    <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-library">
                        {localize(/* @kp-localization user.MvsLibrary */ 'user.MvsLibrary')}
                    </KpChipTag>
                {/if}

                {#if user.role.includes(UserRoles.ROLE_SUPPLIER)}
                    <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-supplier">
                        {localize(/* @kp-localization user.Supplier */ 'user.Supplier')}
                    </KpChipTag>
                {/if}
            </div>
        </Flex>

        <Spacer flex="1"/>

        <KpUserBarcodeCard {user}/>
    </div>

    <div class="user-attention-requiring-actions">
        {#each detailView.userAttentionRequiringActions as userAttentionRequiringAction}
            <div class="user-attention-requiring-action">
                <WarningBanner>
                    <svelte:fragment slot="text">
                        {userAttentionRequiringAction.text}
                    </svelte:fragment>

                    <KpDefinedActionButton dataQa="extend-registration-button" action="{userAttentionRequiringAction.action}">
                        {userAttentionRequiringAction.actionText}
                    </KpDefinedActionButton>
                </WarningBanner>
            </div>
        {/each}
    </div>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.media-queries.less";

    .kp-user-account-heading-part {
        display: flex;
        flex-direction: column;
        gap: @spacing-sm;

        .user-attention-requiring-action {
            margin-top: @spacing-ml;
        }

        .heading-and-card-container {
            display: flex;
            justify-content: space-between;

            @media (max-width: @screen-xs-max) {
                flex-direction: column;
                gap: @spacing-ml;
            }
        }

        .heading-row {
            display: flex;
            align-items: baseline;
            gap: @spacing-m;

            &.email-row {
                margin-bottom: 3px;
            }

            &:empty {
                display: none;
            }
        }

        .roles-tags-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 4px;
        }
    }

    :global {
        .kp-user-account-heading-part {
            .username-chip-tag {
                background-color: #FFF2F2;
                color: var(--danger-red);
                border: none;
            }

            .kp-barcode-card-container {
                align-items: flex-end !important;

                @media (max-width: @screen-xs-max) {
                    align-items: center !important;
                }
            }
        }
    }
</style>