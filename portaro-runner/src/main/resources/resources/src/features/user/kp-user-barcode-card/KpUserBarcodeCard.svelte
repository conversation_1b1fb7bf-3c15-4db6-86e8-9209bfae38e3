<script lang="ts">
    import type {User} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {onMount} from 'svelte';
    import {getInjector, getLogger} from 'core/svelte-context/context';
    import {WalletBarcodeCardService} from 'src/features/user/kp-user-barcode-card/wallet-barcode-card.service';
    import {isAppleDevice, isAndroidDevice} from 'shared/utils/custom-utils';
    import cardBgImg from './assets/reader-card-bg.webp';
    import googlePayIcon from './assets/googlepay.png';
    import appleWalletIcon from './assets/applewallet.png';
    import verbisLogo from './assets/verbis-logo.png';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';

    export let user: User;

    const service = getInjector().getByClass(WalletBarcodeCardService);
    const logger = getLogger();
    const barCode = user.readerAccounts[0].barCode;

    let cardElement: HTMLDivElement;
    let barcodeElement: SVGSVGElement;
    let barcodeLoading = true;
    let downloadLoading = false;

    // Device support detection
    let supportsAppleWallet = false;
    let supportsGooglePay = false;

    onMount(async () => {
        // Detect device support for wallet features
        supportsAppleWallet = isAppleDevice();
        supportsGooglePay = isAndroidDevice();

        try {
            const JsBarcode = await import(/* webpackChunkName: "jsbarcode" */ 'jsbarcode');

            JsBarcode.default(barcodeElement, barCode, {
                format: 'CODE128',
                lineColor: '#000',
                background: '#********',
                margin: 0,
                height: 50,
                displayValue: false
            });

            barcodeLoading = false;
        } catch (error) {
            logger.error('Failed to load JsBarcode library:', error);
            barcodeLoading = false;
        }
    });

    const handleCardDownload = async () => {
        if (downloadLoading) {
            return;
        }

        try {
            downloadLoading = true;

            const {toCanvas} = await import(/* webpackChunkName: "html-to-image" */ 'html-to-image');

            // Create a canvas element from the card element
            const canvas = await toCanvas(cardElement, {
                backgroundColor: null,
                pixelRatio: 2
            });
            const image = canvas.toDataURL('image/png');

            // Create a temporary link and click it to trigger the download
            const link = document.createElement('a');
            link.href = image;
            link.download = 'karticka-ctenare.png';
            link.click();

            // Cleanup
            link.remove();
            canvas.remove();
        } catch (error) {
            logger.error('Failed to download card:', error);
        } finally {
            downloadLoading = false;
        }
    };

    const handleGooglePayClick = async () => {
        await service.generateGoogleWalletCard();
    };

    const handleAppleWalletClick = async () => {
        await service.generateAppleWalletCard();
    };
</script>

<Flex class="kp-barcode-card-container" direction="column" gap="ml">
    <div class="user-barcode-card" bind:this={cardElement}>
        <img alt="card-background" src="{cardBgImg}" class="card-background"/>

        <span class="card-title">Kartička čtenáře knihovny</span>
        <span class="name">{pipe(user, loc())}</span>

        <div class="barcode-container">
            <div class="barcode-image-container">
                <svg class="barcode" bind:this={barcodeElement}></svg>
            </div>

            <Flex alignItems="center" gap="s">
                <span class="barcode-value">{barCode}</span>
                <KpVerticalSeparator height="6px"/>
                <span class="code-label">CODE128</span>
            </Flex>
        </div>

        <img src="{verbisLogo}" height="20px" class="logo" alt="verbis.io"/>
    </div>

    <Flex alignItems="center" columnGap="sm" rowGap="s" wrap="wrap">
        <KpButton buttonSize="sm" buttonStyle="primary" on:click={handleCardDownload} isDisabled={downloadLoading || barcodeLoading}>
            <IconedContent icon="download">
                {downloadLoading ? 'Stahování...' : 'Stáhnout kartičku jako obrázek'}
            </IconedContent>
        </KpButton>

        {#if supportsAppleWallet}
            <KpButton buttonSize="sm" on:click={handleAppleWalletClick}>
                <IconedContent>
                    <svelte:fragment slot="icon">
                        <img class="wallet-icon" src="{appleWalletIcon}" alt="Apple Wallet"/>
                    </svelte:fragment>

                    Uložit do Apple Wallet
                </IconedContent>
            </KpButton>
        {/if}

        {#if supportsGooglePay}
            <KpButton buttonSize="sm" on:click={handleGooglePayClick}>
                <IconedContent icon="print">
                    <svelte:fragment slot="icon">
                        <img class="wallet-icon" src="{googlePayIcon}" alt="Google Pay"/>
                    </svelte:fragment>

                    Uložit do Google Pay
                </IconedContent>
            </KpButton>
        {/if}
    </Flex>
</Flex>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @card-padding: 24px;

    .user-barcode-card {
        max-width: 400px;
        max-height: 220px;
        aspect-ratio: 1.818181;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        overflow: hidden;
        isolation: isolate;
        padding: @card-padding;
        position: relative;
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
        color: white;
        box-shadow: rgba(0, 0, 0, 0.07) 0 1px 2px, rgba(0, 0, 0, 0.07) 0 2px 4px, rgba(0, 0, 0, 0.07) 0 4px 8px, rgba(0, 0, 0, 0.07) 0 8px 16px, rgba(0, 0, 0, 0.07) 0 16px 32px, rgba(0, 0, 0, 0.07) 0 32px 64px;

        .card-title {
            font-size: @font-size-small;
            opacity: 0.75;
        }

        .name {
            font-size: @font-size-xl;
            font-weight: 500;
        }

        .barcode-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
            margin-top: auto;

            .barcode-image-container {
                padding: @spacing-s;
                background-color: white;
                border-radius: @border-radius-large;
                display: flex;
                width: min-content;
            }

            .barcode-value {
                font-size: @font-size-small;
            }

            .code-label {
                font-size: @font-size-xs;
                opacity: 0.75;
            }
        }

        .card-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
        }

        .logo {
            position: absolute;
            top: @card-padding;
            right: @card-padding;
        }
    }

    .wallet-icon {
        height: 11px;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 0.75;
        }
        50% {
            opacity: 0.4;
        }
    }
</style>