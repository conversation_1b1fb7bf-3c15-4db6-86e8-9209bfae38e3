<script lang="ts">
    import type {ExemplarPlacementsData, RegalMap} from './types';
    import {getInjector} from 'core/svelte-context/context';
    import {KpExemplarPlacementsService} from './kp-exemplar-placements.service';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {removeAll, replaceAll, byIdOf, byReference} from 'shared/utils/array-utils';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import ExemplarPlacementsHeadingPart from './parts/ExemplarPlacementsHeadingPart.svelte';
    import ExemplarPlacementsAddFormPart from './parts/ExemplarPlacementsAddFormPart.svelte';
    import ExemplarPlacementsTablePart from './parts/ExemplarPlacementsTablePart.svelte';

    const service = getInjector().getByToken<KpExemplarPlacementsService>(KpExemplarPlacementsService.serviceName);

    let loading = true;
    let addingNewMap = false;
    let pageData: ExemplarPlacementsData;

    onMount(async () => {
        pageData = await service.loadExemplarPlacementsPageData();
        loading = false;
    });

    const handleAddNewMapClick = () => {
        addingNewMap = true;
    }

    const handleAddNewMapCancelled = () => {
        addingNewMap = false;
    }

    const handleNewMapAdded = (event: CustomEvent<RegalMap>) => {
        pageData.maps.push(event.detail);
        pageData = pageData; // Trigger reactivity
        addingNewMap = false;
    }

    const handleRegalMapEdited = (event: CustomEvent<RegalMap>) => {
        const map = event.detail;
        const maps = replaceAll(pageData.maps, byIdOf(map), map);
        pageData = {...pageData, maps};
    }

    const handleRegalMapRemoved = (event: CustomEvent<RegalMap>) => {
        const maps = removeAll(pageData.maps, byReference(event.detail));
        pageData = {...pageData, maps};
    }
</script>

<KpLoadablePageContainer pageClass="kp-exemplar-placements-page"
                         loadError="{!loading && !exists(pageData)}"
                         gap="0px"
                         {loading}>

    <ExemplarPlacementsHeadingPart on:add-new={handleAddNewMapClick}/>

    <ExemplarPlacementsAddFormPart {pageData}
                                   {addingNewMap}
                                   on:cancel={handleAddNewMapCancelled}
                                   on:map-add={handleNewMapAdded}/>

    <ExemplarPlacementsTablePart {pageData}
                                 on:map-edit={handleRegalMapEdited}
                                 on:map-remove={handleRegalMapRemoved}/>
</KpLoadablePageContainer>