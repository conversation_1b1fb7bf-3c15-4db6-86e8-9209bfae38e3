@import "~bootstrap-less/bootstrap/variables";

jp-slider {
	display: block;
	position: relative;

	.arrow {
		@arrow-thickness: 3px;
		@arrow-size: 5px;

		background-color: @brand-primary;
		width: 50px;
		height: 50px;
		border-radius: 50%;
		position: absolute;
		top: 50%;
		z-index: 100;
		opacity: 0.5;
		transition: opacity 0.3s ease;
		cursor: pointer;

		&::before {
			content: "";
			border: solid white;
			border-width: 0 @arrow-thickness @arrow-thickness 0;
			display: inline-block;
			padding: @arrow-size;
			position: relative;
			top: 16px;
			left: 16px;
		}

		&:hover {
			opacity: 1;
		}

		&.arrow-prev {
			left: 30px;
			transform: rotate(135deg);
			-webkit-transform: rotate(135deg);
		}

		&.arrow-next {
			right: 30px;
			transform: rotate(-45deg);
			-webkit-transform: rotate(-45deg);
		}
	}

	.slick {
		max-width: 100%;
		margin: 0 auto;
	}
}