<#ftl outputFormat="HTML">
<#import "../../../../../freemarker/_localization.ftl" as loc>

<div class="container-fluid kp-carousel" ng-cloak>

    <div class="carousel-background blur"></div>
    <div class="carousel-background-overlay"></div>

    <div class="container carousel-container">
        <div class="row">
            <div class="col-xs-12">
                <button type="button" id="btn-carousel-prev" class="btn btn-primary btn-carousel-arrow btn-slider btn-slider-prev btn btn-default glyphicon glyphicon-chevron-left"></button>
                <button type="button" id="btn-carousel-next" class="btn btn-primary btn-carousel-arrow btn-slider btn-slider-next btn btn-default glyphicon glyphicon-chevron-right"></button>

                <jp-slider ng-if="$ctrl.items.length > 0" mode="multiple" settings="$ctrl.settings" class="carousel-items-container">

                    <div ng-repeat="item in $ctrl.items">

                        <div class="carousel-item-wrapper">
                            <div ng-if="item.cover" class="item-cover cover-container cover-container-in-carousel">
                                <kp-svelte-component-wrapper component="::$ctrl.kpCoverComponent" props="::{record: item, fillTo: 'height'}"></kp-svelte-component-wrapper>
                            </div>
                            <div class="item-desc">
                                <kp-svelte-component-wrapper component="::$ctrl.kpCustomParagraph" props="::{content: item.exports.portaroRecordNewsParagraph}"></kp-svelte-component-wrapper>
                                <div class="item-footer">
                                    <a class="btn btn-primary carousel-more-button" ng-href="{{item | kpRecordDetailPath}}">${loc.loc("commons.ZobrazitVice")?noEsc}</a>
                                </div>
                            </div>
                        </div>

                    </div>

                </jp-slider>

            </div>
        </div>
    </div>

    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" class="blur-svg">
        <defs>
            <filter id="blur-filter">
                <feGaussianBlur stdDeviation="7"></feGaussianBlur>
            </filter>
        </defs>
    </svg>
</div>