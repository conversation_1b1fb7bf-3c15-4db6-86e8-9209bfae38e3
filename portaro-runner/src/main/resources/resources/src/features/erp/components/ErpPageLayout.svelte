<script lang="ts">
    import type {CssSize} from 'shared/ui-widgets/types';
    import type {Auth} from 'typings/portaro.be.types';
    import {exists} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {onDestroy, onMount} from 'svelte';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {ErpSidebarNavService} from 'src/features/erp/components/erp-sidebar-nav/erp-sidebar-nav.service';
    import CurrentAuthService from 'shared/services/current-auth.service';
    import LoginService from 'shared/login/login.service';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import InfoBanner from 'shared/ui-widgets/banner/InfoBanner.svelte';
    import sutin2Logo from '../../sutor/assets/sutin-2-logo.svg?assets';
    import ErpBlurredIconsBackground from 'src/features/erp/components/erp-blurred-bg/ErpBlurredIconsBackground.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const loginService = getInjector().getByClass(LoginService);
    const erpSidebarNavService = getInjector().getByClass(ErpSidebarNavService);
    const localize = getLocalization();

    export let pageClass: string;
    export let loading = false;
    export let loadError = false;
    export let gap: CssSize = '36px';
    export let withoutContentPadding = false;
    export let showHeadingWhenNotLoggedIn = false;
    export let dontCollapseSidebar = false;
    export let withoutLogo = false;

    const pageFlyInParams = {y: 15, duration: 250};

    let auth: Auth;
    const currentAuthSubscription = currentAuthService.currentAuth$().subscribe((currentAuth) => auth = currentAuth);

    onMount(() => {
        if (dontCollapseSidebar) {
            return;
        }

        erpSidebarNavService.toggleCollapse(true);
    });

    onDestroy(() => {
        unsubscribeAllSubscriptions(currentAuthSubscription);
    });
</script>

{#if loading}
    <div class="loading-container" in:fly={pageFlyInParams}>
        <KpLoadingBlock/>
    </div>
{/if}

{#if loadError}
    <div class="loading-container load-error" in:fly={pageFlyInParams}>
        <IconedContent align="center"
                       iconColor="var(--danger-red)"
                       justify="center"
                       orientation="vertical"
                       icon="exclamation">

            Nastala chyba při načítání!
        </IconedContent>
    </div>
{/if}

{#if !loading && !loadError}
    {#key auth.evided}
        <div class="erp-page-layout {pageClass}-container" in:fly={pageFlyInParams}>
            {#if exists($$slots.heading) && (auth.evided || showHeadingWhenNotLoggedIn)}
                <slot name="heading" {auth}/>
            {/if}

            <div class="page-layout-content {pageClass}"
                 class:content-padded={!withoutContentPadding || !auth.evided}
                 class:top-padded={(exists($$slots.heading) && !withoutContentPadding) || !auth.evided}
                 style:--gap="{gap}">

                {#if auth.evided}
                    <slot {auth}/>
                {:else}
                    {#if exists($$slots['no-auth-content'])}
                        <slot name="no-auth-content"/>
                    {/if}

                    {#if !exists($$slots['no-auth-content'])}
                        <div class="info-banner-container">
                            {#if !withoutLogo}
                                <img class="sutin-logo" src="{sutin2Logo}" alt="Sutin 2 logo"/>
                                <ErpBlurredIconsBackground/>

                                <div class="heading-texts-container">
                                    <KpHeading type="h1">
                                        Přihlašte se do systému, prosím
                                    </KpHeading>

                                    <span class="text-muted text-center">
                                        Zbrusu nový, přehledný a svižný systém Sutin
                                    </span>
                                </div>
                            {/if}

                            <div class="info-banner">
                                <InfoBanner>
                                    <svelte:fragment slot="text">
                                        Nejste přihlášeni. Pro odemčení funkcionalit systému se přihlaste, prosím.
                                    </svelte:fragment>

                                    <KpButton on:click={() => loginService.login()}>
                                        <IconedContent icon="sign-in-alt">
                                            {localize(/* @kp-localization login.loginButton */ 'login.loginButton')}
                                        </IconedContent>
                                    </KpButton>
                                </InfoBanner>
                            </div>
                        </div>
                    {/if}
                {/if}
            </div>
        </div>
    {/key}
{/if}

<style lang="less">
    @import (reference) "styles/portaro-erp.less";

    .erp-page-layout {
        .flex-grow();
        position: relative;

        .page-layout-content {
            .flex-grow();
            gap: var(--gap);

            &.content-padded {
                padding: 0 @main-padding-horizontal @main-padding-vertical;
            }

            &.top-padded {
                padding-top: @main-padding-vertical;
            }

            &.full-padded {
                padding: @main-padding-horizontal @main-padding-vertical;
            }

            .info-banner-container {
                .flex-grow();
                align-items: center;
                justify-content: center;
                isolation: isolate;
                gap: @spacing-xxl;

                .sutin-logo {
                    height: 100px;
                }

                .heading-texts-container {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    gap: @spacing-s;
                }

                .info-banner {
                    width: 760px;
                    max-width: 80vw;
                }
            }
        }
    }

    :global {
        .erp-page-layout .erp-tabbed-subpages-container {
            .record-grid-tab-page {
                .kp-loading-wrapper {
                    position: absolute;
                    top: 47.5%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }

                .kp-record-grid-container {
                    .flex-grow();
                    .record-grid-display-flex-grow();
                }

                .record-grid-display-flex-grow();

                .kp-search-toolbar {
                    padding-inline: @spacing-xxl;
                    padding-top: @spacing-ml;
                }
            }
        }
    }
</style>