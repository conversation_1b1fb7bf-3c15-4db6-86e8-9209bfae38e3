<script lang="ts">
    import {SummarySectionType} from 'src/features/sutor/components/attendance-calendar/types';
    import AttendanceSummarySection from '../AttendanceSummarySection.svelte';
    import AttendanceCell from '../../AttendanceCell.svelte';
    import AttendanceSummaryColumn from '../AttendanceSummaryColumn.svelte';
</script>

<AttendanceSummarySection type="{SummarySectionType.SECTION_LEGEND}">
    <AttendanceSummaryColumn>
        <AttendanceCell header></AttendanceCell>
        <AttendanceCell>Náklady:</AttendanceCell>
        <AttendanceCell>PwK:</AttendanceCell>
        <AttendanceCell>Uznáno:</AttendanceCell>
    </AttendanceSummaryColumn>
</AttendanceSummarySection>