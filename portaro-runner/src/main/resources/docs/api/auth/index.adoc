== Authentication

Authentication to Portaro uses standard OAuth2 client or authorization code flows.

== Get token

=== for client (e.g. Verbis app)

[source,http,options="nowrap"]
----
POST /oauth/token HTTP/1.1
Content-Type: application/json; charset=UTF-8
Authorization: Basic <base64encodedclientandpassword>
Host: https://demo.kpsys.cz

grant_type:client_credentials
----

==== Example cURL
----
curl --request POST --url http://localhost/oauth/token --header "Authorization: Basic <base64encodedclientandpassword>" --data grant_type=client_credentials
----

=== for user via client

[source,http,options="nowrap"]
----
POST /oauth/token HTTP/1.1
Content-Type: application/json; charset=UTF-8
Authorization: Basic <base64encodedclientandpassword>
Host: https://demo.kpsys.cz

grant_type:password
username:<knihovnikovo_jmeno>
password:<knihovnikovo_heslo>
----

==== Example cURL
----
curl --request POST --url http://localhost/oauth/token --header "Authorization: Basic <base64encodedclientandpassword>" --data grant_type=password --data username=<knihovnikovo_jmeno> --data password=<knihovnikovo_heslo>
----

== Response
=== Example
----
{
  "access_token": "<access_token>",
  "token_type": "bearer",
  "expires_in": 43199,
  "scope": "EVIDED",
  "jti": "dd6d6306-a13e-46d6-8379-92b9096fbd13"
}
----

== Subsequent authorized calls

To all requests, add header
----
Authorization: Bearer <access_token>
----

=== Example cURL
----
curl --request GET --url http://localhost/api/users/current --header "Authorization: Bearer <access_token>"
----