package cz.kpsys.portaro.config;

import cz.kpsys.portaro.app.ssl.HttpsKeysSource;
import cz.kpsys.portaro.auth.internal.InternalLoginCredentialsProperties;
import cz.kpsys.portaro.commons.barcode.BarCodeType;
import cz.kpsys.portaro.commons.date.LocalTimeRange;
import cz.kpsys.portaro.commons.ip.IpAddress;
import cz.kpsys.portaro.commons.ip.IpAddressRange;
import cz.kpsys.portaro.commons.mail.SmtpTransportStrategy;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.exemplar.ExemplarSettingKeys;
import cz.kpsys.portaro.loan.LoanSettingKeys;
import cz.kpsys.portaro.pops.agreement.AgreementStyle;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.record.comment.CommentStyle;
import cz.kpsys.portaro.record.link.SearchElsewhereItem;
import cz.kpsys.portaro.record.view.FieldTypeFilter;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingKey;
import cz.kpsys.portaro.shutdown.RestartingPolicy;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import static cz.kpsys.portaro.app.CatalogConstants.Ini.*;
import static cz.kpsys.portaro.user.UserSettingKeys.SECTION_CTEN;
import static cz.kpsys.portaro.user.UserSettingKeys.SECTION_OPAC_USER;

public class SettingKeys {

    // WEB
    public static final SettingKey<@NonNull RestartingPolicy> PERIODIAL_RESTARTING = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "PeriodicalRestarting");
    public static final SettingKey<@NonNull Boolean> FORCE_HTTPS_ENABLED = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "ForceHttps");
    public static final SettingKey<@NonNull Boolean> SERVER_REVERSE_PROXY = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "BehindReverseProxy");
    public static final SettingKey<IpAddress> SERVER_LISTEN_IP = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "ListenIp");
    public static final SettingKey<String> ACME_CERTIFICATE_LOCATION = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "AcmeCertificateLocation");
    public static final SettingKey<String> ACME_PRIVATE_KEY_LOCATION = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "AcmePrivateKeyLocation");
    public static final SettingKey<String> ACME_CHALLENGE_KEY = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "AcmeChallengeKey");
    public static final SettingKey<@NonNull Boolean> INTERNAL_HTTPS_ENABLED = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsEnabled");
    public static final SettingKey<Integer> INTERNAL_HTTPS_PORT = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsPort");
    public static final SettingKey<HttpsKeysSource> INTERNAL_HTTPS_KEYS_SOURCE = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsKeysSource");
    public static final SettingKey<String> INTERNAL_HTTPS_CERTIFICATE_LOCATION = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsCertificateLocation");
    public static final SettingKey<String> INTERNAL_HTTPS_CA_CERTIFICATE_LOCATION = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsCACertificateLocation");
    public static final SettingKey<String> INTERNAL_HTTPS_PRIVATE_KEY_LOCATION = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsPrivateKeyLocation");
    public static final SettingKey<String> INTERNAL_HTTPS_PRIVATE_KEY_PASSWORD = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsPrivateKeyPassword");
    public static final SettingKey<String> INTERNAL_HTTPS_KEYSTORE_LOCATION = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsKeystoreLocation");
    public static final SettingKey<String> INTERNAL_HTTPS_KEYSTORE_PASSWORD = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsKeystorePassword");
    public static final SettingKey<String> INTERNAL_HTTPS_KEYSTORE_KEY_ALIAS = new SettingKey<>(CoreSettingKeys.SECTION_OPAC_WEB, "InternalHttpsKeystoreKeyAlias");

    /**
     * Rozsah vnitrnich ip adres knihovny.
     */
    public static final SettingKey<@NonNull IpAddressRange> INTERNAL_IP_ADDRESS_RANGE = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "VnitrniIPAdresyRozsah");

    /**
     * Seznam adres, ktere jsou explicitne nastavene jako vnitrni (nejcasteji to bude pouze loopback - 127.0.0.1, ale pokud budeme chtit nastavit jako vnitrni i jine pobocky, tak zde budou jejich konkretni ip).
     */
    public static final SettingKey<List<IpAddress>> INTERNAL_IP_ADDRESSES = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "VnitrniIPAdresy");

    /**
     * Seznam adres, ktere jsou explicitne nastavene jako vnejsi (napr. pri pouzivani proxy serveru nastavime, ze vse co bude mit adresu proxy serveru, bude zvenku).
     */
    public static final SettingKey<List<IpAddress>> EXTERNAL_IP_ADDRESSES = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "VnejsiIPAdresy");



    // PORTARO COMMONS
    public static final SettingKey<@NonNull String> SERIAL_CODE = new SettingKey<>(SECTION_KPWIN, "SERIAL");
    public static final SettingKey<String> KPSYS_API_KEY = new SettingKey<>(SECTION_KPWIN, "KpsysApiKey");
    public static final SettingKey<String> LICENCE_KEY = new SettingKey<>(SECTION_KPWIN, "LicenceKey");
    public static final SettingKey<@NonNull Boolean> CENTRAL_INDEX_ENABLED = new SettingKey<>(SECTION_CENTRAL_INDEX, "Zapnuto");
    public static final SettingKey<@NonNull List<String>> SERVICES = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "Services");
    public static final SettingKey<@NonNull Map<Integer, String>> BUILT_IN_COVERS_BY_FONDS = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "BuiltInDefaultCovers");
    public static final SettingKey<@NonNull List<Locale>> ENABLED_LOCALES = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "Locales");
    public static final SettingKey<@NonNull List<String>> FORBIDDEN_MENU_ITEMS = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "ForbiddenMenuItems");
    public static final SettingKey<@NonNull Boolean> CAMERA_SCANNER_ENABLED = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "CameraScanning");
    public static final SettingKey<@NonNull Boolean> APPSERVER_ENABLED = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "AppserverEnabled");

    public static final SettingKey<@NonNull Boolean> DEPARTMENT_CREATION_ACCEPTABLE_PARENT_DEPARTMENTS_SHOW_ALL_CHILDS = new SettingKey<>(SECTION_OPAC_DEPARTMENT, "CreationAcceptableParentDepartmentsShowAllChildren");
    public static final SettingKey<@NonNull List<Integer>> DEPARTMENT_CREATION_ACCEPTABLE_PARENT_DEPARTMENTS = new SettingKey<>(SECTION_OPAC_DEPARTMENT, "CreationAcceptableParentDepartments");
    public static final SettingKey<@NonNull Boolean> DEPARTMENT_CREATION_CUSTOM_PARENT_DOMAIN_ENABLED = new SettingKey<>(SECTION_DEPARTMENT_DOMAINED, "customParentDomainEnabled");
    public static final SettingKey<@NullableNotBlank String> DEPARTMENT_CREATION_CUSTOM_PARENT_DOMAIN_VALUE = new SettingKey<>(SECTION_DEPARTMENT_DOMAINED, "customParentDomainValue");



    // LIBRARY
    public static final SettingKey<@NullableNotBlank String> LIBRARY_NAME = new SettingKey<>(SECTION_KNIHOVNA, "NAZEV");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_STREET = new SettingKey<>(SECTION_KNIHOVNA, "ULICE");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_POSTAL_CODE = new SettingKey<>(SECTION_KNIHOVNA, "PSC");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_CITY = new SettingKey<>(SECTION_KNIHOVNA, "MISTO");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_EMAIL = new SettingKey<>(SECTION_KNIHOVNA, "EMAIL");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_PHONE_NUMBER = new SettingKey<>(SECTION_KNIHOVNA, "TELEFON1");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_CIN = new SettingKey<>(SECTION_KNIHOVNA, "ICO");
    public static final SettingKey<@NullableNotBlank String> LIBRARY_VATIN = new SettingKey<>(SECTION_KNIHOVNA, "DICO");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_MONDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningMonday");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_TUESDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningTuesday");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_WEDNESDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningWednesday");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_THURSDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningThursday");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_FRIDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningFriday");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_SATURDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningSaturday");
    public static final SettingKey<@NonNull List<LocalTimeRange>> LIBRARY_OPENING_SUNDAY = new SettingKey<>(SECTION_KNIHOVNA, "OpeningSunday");



    // EMAIL
    public static final SettingKey<@NonNull Boolean> SMTP_USE_SENDER_AND_FROM = new SettingKey<>(SECTION_EMAIL, "SMTP_USE_SENDER_AND_FROM");
    public static final SettingKey<@NullableNotBlank String> SMTP_FROM_LIBRARY_MAILS_REPLY_TO_ADDRESS = new SettingKey<>(SECTION_EMAIL, "SMTP_REPLYTO_ADDRESS");
    public static final SettingKey<@NullableNotBlank String> SMTP_SENDER_ADDRESS = new SettingKey<>(SECTION_EMAIL, "SMTP_SENDER_ADDRESS");
    public static final SettingKey<String> SMTP_SENDER_NAME = new SettingKey<>(SECTION_EMAIL, "SMTP_SENDER_NAME");

    public static final SettingKey<@NonNull SmtpTransportStrategy> SMTP_TRANSPORT_STRATEGY = new SettingKey<>(SECTION_EMAIL, "MAIL_PROVIDER");
    public static final SettingKey<@NonNull Boolean> SMTP_AUTHENTICATION_ENABLED = new SettingKey<>(SECTION_EMAIL, "SMTP_AUTH");
    public static final SettingKey<@NullableNotBlank String> SMTP_SERVER_ADDRESS = new SettingKey<>(SECTION_EMAIL, "SMTP_SERVER_ADDRESS");
    public static final SettingKey<@NonNull Integer> SMTP_SERVER_PORT = new SettingKey<>(SECTION_EMAIL, "SMTP_SERVER_PORT");
    public static final SettingKey<@NullableNotBlank String> SMTP_SERVER_USERNAME = new SettingKey<>(SECTION_EMAIL, "SMTP_USER");
    public static final SettingKey<@NullableNotBlank String> SMTP_SERVER_PASSWORD = new SettingKey<>(SECTION_EMAIL, "SMTP_PASSWORD");


    // SMS
    public static final SettingKey<@NonNull Boolean> SMS_ENABLED = new SettingKey<>(SECTION_SMS, "enabled");



    // USER & AUTH
    public static final SettingKey<@NonNull List<InternalLoginCredentialsProperties>> USER_INTERNAL_LOGIN_CREDENTIALS_PROPERTIES = new SettingKey<>(SECTION_OPAC_USER, "Login");
    public static final SettingKey<List<String>> USER_REQUIRED_FIELDS = new SettingKey<>(SECTION_OPAC_USER, "ReaderRequiredProperties");
    public static final SettingKey<List<String>> USER_REQUIRED_FIELDS_FOR_LIBRARIAN = new SettingKey<>(SECTION_OPAC_USER, "ReaderRequiredPropertiesForLibrarian");
    public static final SettingKey<@NonNull Boolean> USER_MAIL_ACTIVATION_ENABLED = new SettingKey<>(SECTION_OPAC_USER, "UserMailActivation");
    public static final SettingKey<List<String>> USER_SELF_CREATION_FIELDS = new SettingKey<>(SECTION_OPAC_USER, "VlastnostiCtenareVPlneRegistraci");
    public static final SettingKey<Map<String, List<String>>> USER_SELF_CREATION_FIELDS_BY_CATEGORY = new SettingKey<>(SECTION_OPAC_USER, "UserSelfCreationFieldsByCategory");
    public static final SettingKey<@NonNull List<String>> USER_SELF_EDITATION_FIELDS = new SettingKey<>(SECTION_OPAC_USER, "CtenaremEditovatelneVlastnostiCtenare");
    public static final SettingKey<@NonNull List<String>> READER_EDITABLE_FIELDS_BY_EDITOR = new SettingKey<>(SECTION_OPAC_USER, "ReaderEditableFieldsByEditor");
    public static final SettingKey<@NonNull Boolean> FULL_REGISTRATION_ENABLED = new SettingKey<>(SECTION_OPAC_USER, "FullRegistration");
    public static final SettingKey<@NonNull Boolean> FORGOTTEN_CREDENTIALS_ENABLED = new SettingKey<>(SECTION_OPAC_USER, "ForgottenCredentialsEnabled");
    public static final SettingKey<@NonNull Boolean> MONEY_ENABLED = new SettingKey<>(CoreSettingKeys.SECTION_OPAC, "Money");
    public static final SettingKey<@NullableNotBlank String> DEFAULT_BY_READER_FULL_REGISTRATION_READER_CATEGORY = new SettingKey<>(SECTION_OPAC_USER, "DefaultByReaderFullRegistrationReaderCategory");
    public static final SettingKey<@Nullable Integer> DEFAULT_EDITOR_ACCOUNT_GROUP = new SettingKey<>(SECTION_OPAC_USER, "DefaultEditorAccountGroupId");
    public static final SettingKey<@NonNull Boolean> SHOW_USER_REGISTRATION_AGREEMENT_PRINT_BUTTON = new SettingKey<>(SECTION_OPAC_USER, "ShowUserRegistrationAgreementPrintButton");
    public static final SettingKey<@NonNull Boolean> SHOW_USER_RECEIPT_OF_PAYMENT_PRINT_BUTTON = new SettingKey<>(SECTION_OPAC_USER, "ShowUserReceiptOfPaymentPrintButton");
    public static final SettingKey<@NonNull Boolean> SHOW_USER_LOAN_CONFIRMATION_PRINT_BUTTON = new SettingKey<>(SECTION_OPAC_USER, "ShowUserLoanConfirmationPrintButton");
    public static final SettingKey<@NonNull Boolean> SHOW_USER_PREFERENCES_BUTTON = new SettingKey<>(SECTION_OPAC_USER, "ShowUserPreferencesButton");
    public static final SettingKey<@NonNull Boolean> READER_SELF_REGISTRATION_EXTEND_FOR_ALL_USERS = new SettingKey<>(SECTION_OPAC_USER, "ReaderSelfRegistrationExtendForAllUsers");
    public static final SettingKey<@NonNull Boolean> AUTOGENERATE_USER_CARD_NUMBER = new SettingKey<>(SECTION_CTEN, "AutoGenCisLeg");
    public static final SettingKey<@NonNull Boolean> AUTOGENERATE_USER_BARCODE = new SettingKey<>(SECTION_CTEN, "AutoGenBarCod");
    public static final SettingKey<List<String>> READER_SELF_REGISTRATION_EXTEND_READER_CATEGORIES = new SettingKey<>(SECTION_OPAC_USER, "ReaderSelfRegistrationExtendReaderCategories");
    public static final SettingKey<@NonNull Boolean> SHOW_TERMS_AND_CONDITIONS_IF_EXTENDING_REGISTRATION = new SettingKey<>(SECTION_OPAC_USER, "ShowTermsAndConditionsIfExtendingRegistration");
    public static final SettingKey<@NonNull Boolean> SHOW_USER_STATEMENT_IF_EXTENDING_REGISTRATION = new SettingKey<>(SECTION_OPAC_USER, "ShowUserStatementIfExtendingRegistration");
    public static final SettingKey<Integer> REGISTRATION_PERIOD_EXTENSION_THRESHOLD_DAYS = new SettingKey<>(SECTION_OPAC_USER, "RegistrationPeriodExtensionThresholdDays");


    // RECORD
    public static final SettingKey<List<Integer>> BUILDINGS_DEFAULT = new SettingKey<>(SECTION_CTEN, "INIT_BUDOVY");
    public static final SettingKey<@NonNull Boolean> RECORD_MARC_TAB_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "MarcTab");
    public static final SettingKey<@NonNull Boolean> AUTHORITY_DETAIL_TAB_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "AuthorityDetailTab");
    public static final SettingKey<@NonNull Boolean> DOCUMENT_DETAIL_TAB_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DocumentDetailTab");
    public static final SettingKey<@NonNull Boolean> DOCUMENT_EXEMPLARS_TAB_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DocumentExemplarsTab");
    public static final SettingKey<@NonNull Boolean> SHOW_ALL_RECORD_FIELDS_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DetailTabShowAllFields");
    public static final SettingKey<String> AUTHORITY_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "TemplateAuthorityDetailUnderTitle");
    public static final SettingKey<String> DOCUMENT_UNDER_COVER_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "TemplateDocumentDetailUnderCover");
    public static final SettingKey<String> DOCUMENT_NEXT_TO_HEADER_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "TemplateDocumentDetailRightPanel");
    public static final SettingKey<String> DOCUMENT_NEXT_TO_COVER_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "TemplateDocumentDetailUnderTitle");
    public static final SettingKey<@NonNull Map<Integer, String>> CITATION_DOCTYPES_BY_FONDS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "CitationDoctypes");
    public static final SettingKey<@NonNull String> CITATION_SERVICE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "CitationService");
    public static final SettingKey<@NonNull Integer> SIMILAR_DOCUMENTS_LIMIT = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "SimilarDocumentsCount");
    public static final SettingKey<@Nullable UUID> AUTHORITY_HIERARCHY_ROOT = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "AuthorityHierarchyRoot");
    public static final SettingKey<@NonNull CommentStyle> COMMENT_STYLE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "CommentRestriction");
    public static final SettingKey<List<SearchElsewhereItem>> SEARCH_ELSEWHERE_ITEMS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "SearchElsewhere");
    public static final SettingKey<@NonNull FieldTypeFilter> DOCUMENT_DETAIL_FIELDS_FILTER = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DetailTabFields");
    public static final SettingKey<@NonNull Map<Integer, FieldTypeFilter>> RECORD_DETAIL_FIELDS_FILTERS_BY_FONDS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DetailTabFieldsByFonds");
    public static final SettingKey<@NonNull Boolean> SHOW_EDITED_INDICATORS_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "ShowEditedIndicators");
    public static final SettingKey<@NonNull Boolean> SHOW_EDITED_FIELD_IDENTIFIERS_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "ShowEditedFieldIdentifiers");
    public static final SettingKey<@NonNull Boolean> PUBLISHING_DOCUMENT_FINISHED_CATALOGIZATION = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "PublishingDocumentFinishedCatalogization");
    public static final SettingKey<@NonNull Boolean> DOCUMENT_ARTICLES_TAB_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DocumentArticlesTab");
    public static final SettingKey<@NonNull List<Integer>> DOCUMENT_PARTS_CONTAINING_FIELD_NUMBERS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "DocumentPartsContainingFieldNumbers");
    public static final SettingKey<@NonNull Boolean> RATING_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_RECORD, "RatingEnabled");


    public static final SettingKey<@NonNull Boolean> ONLY_USED_AUTHORITIES_SHOW_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "ShowOnlyUsedAuthorities");
    public static final SettingKey<@NonNull Boolean> MOST_LENT_DOCUMENTS_WITH_PERIODICALS_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "MostLentIncludePerio");
    public static final SettingKey<@NonNull String> GLOBAL_SEARCH_TEMPLATE_1 = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "VzorGlobalniHledaniPokus1");
    public static final SettingKey<String> SEARCH_DOCUMENT_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "TemplateDocumentSearchMain");
    public static final SettingKey<String> SEARCH_DOCUMENT_MORE_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "TemplateDocumentSearchMore");
    public static final SettingKey<String> SEARCH_AUTHORITY_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "TemplateAuthoritySearch");
    public static final SettingKey<@NonNull String> AUTHORITY_GLOBAL_SEARCH_QUERY_TEMPLATE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "AuthorityGlobalSearchTemplate");
    public static final SettingKey<Map<Integer, String>> AUTHORITY_INDEX_PREFIXES_BY_FONDS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "AuthorityIndexPrefixesByFonds");
    public static final SettingKey<@NonNull Integer> BASIC_MODE_SEARCH_FIELDS_COUNT = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "BasicModeSearchFieldsCount");
    public static final SettingKey<List<String>> FORM_SEARCH_FIELDS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "FormSearchFields");
    public static final SettingKey<@NonNull List<String>> DEFAULT_EXPANDED_FACETS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "DefaultneRozbaleneRezy");
    public static final SettingKey<List<String>> FACETS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "Rezy");
    public static final SettingKey<@NonNull Integer> FACET_KEYS_MAX_COUNT = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "MaxKlicuRezu");
    public static final SettingKey<@Nullable Integer> DOCUMENT_MIN_YEAR = new SettingKey<>(SECTION_SEARCH_FACET, "documentMinYear"); // nullable
    public static final SettingKey<@NonNull Integer> DOCUMENT_SEARCH_PAGE_SIZE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "VelikostStrankyDokumentuPlne");
    public static final SettingKey<@NonNull Integer> AUTHORITY_SEARCH_PAGE_SIZE = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "VelikostStrankyAutorit");
    public static final SettingKey<@NonNull List<SortingItem>> SEARCH_SORTINGS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "Sortings");
    public static final SettingKey<@NonNull List<SortingItem>> AUTHORITY_SEARCH_SORTINGS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "AuthoritySortings");
    public static final SettingKey<List<String>> ENABLED_LOCAL_DATASETS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "LocalDatasets");
    public static final SettingKey<@NonNull Boolean> SHOW_ALL_ZSERVERS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "ZServersShowAll");
    public static final SettingKey<List<Integer>> ENABLED_ZSERVERS = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "ZServers");
    public static final SettingKey<@NonNull Boolean> GLOBAL_SEARCH_INPUT_ENABLED = new SettingKey<>(RecordSettingKeys.SECTION_OPAC_SEARCH, "GlobalSearchInputEnabled");
    public static final SettingKey<@NonNull Boolean> FORCE_DISABLE_SEARCH_IN_ROOT_DATASET = new SettingKey<>(SECTION_SEARCH, "forceDisableSearchInRootDataset");


    // EXEMPLAR
    public static final SettingKey<@NonNull List<Integer>> FORBIDDEN_EXEMPLARS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ForbiddenExemplars");
    public static final SettingKey<@NonNull List<Integer>> EXEMPLAR_STATUSES = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ExemplarStatuses");
    public static final SettingKey<@NonNull Boolean> EXEMPLAR_TABS_BY_BUILDINGS_ENABLED = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ExemplarTabsByBuildings");
    public static final SettingKey<@NonNull Boolean> BUILDING_SCOPE_ACCESS_NUMBERS_UNIQUENESS_ENABLED = new SettingKey<>(SECTION_EXEMP, "PRIRUSTKYDLEBUDOV");
    public static final SettingKey<@NonNull Boolean> TITLE_SIGNATURES_ENABLED = new SettingKey<>(SECTION_EXEMP, "POUZIVATTITULOVESIGNATURY");
    public static final SettingKey<@NonNull Boolean> DISCARD_NUMBERS_WITHIN_BUILDING_ENABLED = new SettingKey<>(SECTION_EXEMP, "UBYTKYDLEBUDOV");
    public static final SettingKey<@NonNull String> REGAL_MAP_PROPERTY = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ExemplarPlacementInfoProperty");
    public static final SettingKey<List<String>> ISSUE_COLUMNS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "IssueColumns");
    public static final SettingKey<List<String>> EXEMPLAR_DEFAULT_COLUMNS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ExemplarColumns");
    public static final SettingKey<Map<Integer, List<String>>> EXEMPLAR_COLUMNS_BY_FONDS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ExemplarColumnsByFonds");
    public static final SettingKey<List<String>> EDITABLE_ISSUE_PROPS = new SettingKey<>(LoanSettingKeys.SECTION_EVERBIS, "EditovatelneVlastnostiCisla");
    public static final SettingKey<List<String>> EDITABLE_EXEMPLAR_PROPS = new SettingKey<>(LoanSettingKeys.SECTION_EVERBIS, "EditovatelneVlastnostiExemplare");
    public static final SettingKey<@NonNull Integer> EXEMPLAR_BAR_CODE_MAX_LENGTH = new SettingKey<>(LoanSettingKeys.SECTION_VYPUC, "BCVALIDLEN");
    public static final SettingKey<List<Sort.Order>> EXEMPLAR_SORT_PROPS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "ExemplarSorting");
    public static final SettingKey<List<Sort.Order>> BINDING_SORT_PROPS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "BindingSorting");
    public static final SettingKey<List<Sort.Order>> ISSUE_SORT_PROPS = new SettingKey<>(ExemplarSettingKeys.SECTION_OPAC_EXEMP, "IssueSorting");
    public static final SettingKey<List<BarCodeType>> EXEMPLAR_BARCODE_TYPES = new SettingKey<>(SECTION_EXEMP, "ExemplarBarCodeType");


    public static final SettingKey<@Nullable Integer> EXTERNAL_RESOURCES_FOND = new SettingKey<>(SECTION_EXT_ZDROJE, "EXTZFOND");

    // GDPR
    public static final SettingKey<@NonNull Boolean> SHOW_ANONYMIZATION_QUESTION_DIALOG = new SettingKey<>(SECTION_GDPR, "ShowAnonymizationDialogIfNotSet");

    // POPS
    public static final SettingKey<AgreementStyle> AGREEMENT_STYLE = new SettingKey<>(SECTION_POPS, "AgreementStyle");

    public static final SettingKey<@Nullable Integer> DEFAULT_LOCATION = new SettingKey<>(SECTION_EXEMP, "DEFAULTLOKACE");
    public static final SettingKey<Integer> DEFAULT_STATUS = new SettingKey<>(SECTION_EXEMP, "DEFAULTSTATUS");
    public static final SettingKey<String> DEFAULT_THEMATIC_GROUP = new SettingKey<>(SECTION_EXEMP, "DEFAULTTEMSKUP");
    public static final SettingKey<String> DEFAULT_ACQUISITION_WAY = new SettingKey<>(SECTION_EXEMP, "DEFAULTZPNAB");
    public static final SettingKey<@NullableNotBlank String> DEFAULT_LOAN_CATEGORY = new SettingKey<>(SECTION_EXEMP, "DEFAULTKATVYP");
    public static final SettingKey<@NullableNotBlank String> DEFAULT_READER_CATEGORY = new SettingKey<>(SECTION_CTEN, "DEFKATCTEN");

}
