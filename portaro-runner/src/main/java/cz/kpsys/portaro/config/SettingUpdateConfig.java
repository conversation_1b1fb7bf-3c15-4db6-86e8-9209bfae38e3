package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.config.AppserverConfigService;
import cz.kpsys.portaro.appserver.config.AppserverCustomSettingsRefresher;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.cache.CacheCleaningDeleter;
import cz.kpsys.portaro.commons.cache.CacheCleaningSaver;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.database.FlushingJpaDeleter;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.databasestructure.SettingDb;
import cz.kpsys.portaro.setting.*;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.List;

import static cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings.DBTYPE_FIREBIRD;


@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SettingUpdateConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull EntityManager entityManager;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull DataSource notAutocommitDataSource;
    @NonNull ObjectMapper xmlMapper;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull Provider<Integer> portaroUserIdProvider;
    @NonNull CacheService cacheService;
    @NonNull DatabaseProperties databaseProperties;


    @Bean
    public Runnable saveTransactionAuthenticator() {
        TransactionAuthenticator transactionAuthenticator = new ConditionalTransactionAuthenticator(
                () -> databaseProperties.getType().equals(DBTYPE_FIREBIRD),
                new RelogUserProcedureTransactionAuthenticator(
                        new JdbcTemplate(notAutocommitDataSource),
                        portaroUserIdProvider
                )
        );
        return transactionAuthenticator::authenticate;
    }

    @Bean
    public JpaRepository<CustomSettingEntity, CustomSettingEntityId> customSettingJpaRepository() {
        return new SimpleJpaRepository<>(CustomSettingEntity.class, entityManager);
    }

    @Bean
    public AppserverConfigService appserverConfigService() {
        return new AppserverConfigService(xmlMapper, mappingAppserver, settingLoader.getOnRootProvider(SettingKeys.APPSERVER_ENABLED));
    }

    @Bean
    public AppserverCustomSettingsRefresher appserverCustomSettingsRefresher() {
        return new AppserverCustomSettingsRefresher(xmlMapper, mappingAppserver, settingLoader.getOnRootProvider(SettingKeys.APPSERVER_ENABLED), List.of(SettingDb.INI_FILE.TABLE))
                .afterTransactionCommit(); // when we update ini (e.g. licence key), we're also refreshing appserver cache and it can cause ini load in appserver, which will not see uncommited changes so we must refresh it AFTER commit
    }

    @Bean
    public Saver<CustomSetting<String>, CustomSettingEntity> customSettingSaver() {
        var saver = new GenericHookableSaver<>(
                new PreConvertingSaver<>(
                        new CustomSettingToEntityConverter(),
                        new FlushingJpaSaver<>(customSettingJpaRepository()))
        );

        saver.addPreHook(saveTransactionAuthenticator());

        var transactionalSaver = new TransactionalSaver<>(saver, defaultTransactionTemplateFactory.get());

        return new CacheCleaningSaver<>(
                transactionalSaver,
                List.of(
                        cacheService.createCleanerFor(CustomSetting.class.getSimpleName()),
                        (CacheCleaner) settingLoader,
                        appserverCustomSettingsRefresher()
                )
        );
    }

    @Bean
    public Deleter<CustomSetting<String>> customSettingDeleter() {
        var deleter = new GenericHookableDeleter<>(
                new PreConvertingDeleter<>(
                        new CustomSettingToEntityConverter(),
                        new FlushingJpaDeleter<>(customSettingJpaRepository())));

        deleter.addPreHook(entity -> {
            saveTransactionAuthenticator().run();
            return entity;
        });

        var transactionalDeleter = new TransactionalDeleter<>(deleter, defaultTransactionTemplateFactory.get());

        return new CacheCleaningDeleter<>(
                transactionalDeleter,
                List.of(cacheService.createCleanerFor(CustomSetting.class.getSimpleName()),
                        (CacheCleaner) settingLoader,
                        appserverCustomSettingsRefresher()));
    }
}
