package cz.kpsys.portaro.config;

import cz.kpsys.portaro.calendar.CalendarConfig;
import cz.kpsys.portaro.calendar.CalendarConverterConfig;
import cz.kpsys.portaro.databasebackup.BackupConfig;
import cz.kpsys.portaro.datacopy.DatacopyBatchConfig;
import cz.kpsys.portaro.erp.ErpConfig;
import cz.kpsys.portaro.exemplar.export.ExemplarExportConfig;
import cz.kpsys.portaro.exemplar.imprt.ImporterConfig;
import cz.kpsys.portaro.ext.alive.AliveConfig;
import cz.kpsys.portaro.ext.bakalari.BakalariSynchronizationConfig;
import cz.kpsys.portaro.ext.cpk.CpkConfig;
import cz.kpsys.portaro.ext.cpk.impl.CpkImplConfig;
import cz.kpsys.portaro.ext.edookit.EdookitSynchronizationConfig;
import cz.kpsys.portaro.ext.edupage.EdupageSynchronizationConfig;
import cz.kpsys.portaro.ext.ifis.IfisConfig;
import cz.kpsys.portaro.ext.obalkyknih.ObalkyknihConfig;
import cz.kpsys.portaro.ext.obalkyknih.ObalkyknihServiceConfig;
import cz.kpsys.portaro.ext.powerkey.PowerkeyConfig;
import cz.kpsys.portaro.ext.sol.SolSynchronizationConfig;
import cz.kpsys.portaro.ext.sutin.SutinSynchronizationConfig;
import cz.kpsys.portaro.ext.synchronizer.SynchronizationConfig;
import cz.kpsys.portaro.ext.unis.UnisSynchronizationConfig;
import cz.kpsys.portaro.ext.unob.UnobSynchronizationConfig;
import cz.kpsys.portaro.ext.wallet.WalletConfig;
import cz.kpsys.portaro.ext.ziskej.ZiskejConfig;
import cz.kpsys.portaro.ext.ziskej.impl.ZiskejIllConfig;
import cz.kpsys.portaro.finance.FinanceConfig;
import cz.kpsys.portaro.finance.FinanceConverterConfig;
import cz.kpsys.portaro.integ.feign.FeignConfig;
import cz.kpsys.portaro.loan.*;
import cz.kpsys.portaro.messages.MessageConfig;
import cz.kpsys.portaro.messages.MessageViewConfig;
import cz.kpsys.portaro.oai.provider.impl.OaiProviderConfig;
import cz.kpsys.portaro.proveniencesmap.HistoricalPlaceConfig;
import cz.kpsys.portaro.record.binding.RecordBindingConfig;
import cz.kpsys.portaro.record.export.RecordExportConfig;
import cz.kpsys.portaro.record.grid.RecordTableConfig;
import cz.kpsys.portaro.sip2.server.impl.Sip2ServerConfig;
import cz.kpsys.portaro.thumbnail.ThumbnailConfig;
import cz.kpsys.portaro.user.UserCoreConfig;
import cz.kpsys.portaro.user.payment.provider.csobgw.PaymentCsobGwConfig;
import cz.kpsys.portaro.user.payment.provider.gopay.PaymentGopayConfig;
import cz.kpsys.portaro.user.payment.provider.gpwebpay.PaymentGpwebpayConfig;
import cz.kpsys.portaro.util.UtilConfig;
import cz.kpsys.portaro.verbisbox.info.VerbisboxItemInfoConfig;
import cz.kpsys.portaro.verbisbox.update.VerbisboxUpdateConfig;
import cz.kpsys.portaro.verbisboxer.manager.VerbisboxerManagerConfig;
import cz.kpsys.portaro.verbisboxer.manager.VerbisboxerNotificationConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
        AliveConfig.class,
        BackupConfig.class,
        BakalariSynchronizationConfig.class,
        CalendarConfig.class,
        CalendarConverterConfig.class,
        CpkConfig.class,
        CpkImplConfig.class,
        DatacopyBatchConfig.class,
        EdookitSynchronizationConfig.class,
        EdupageSynchronizationConfig.class,
        ErpConfig.class,
        ExemplarExportConfig.class,
        FeignConfig.class,
        FinanceConfig.class,
        FinanceConverterConfig.class,
        HistoricalPlaceConfig.class,
        IfisConfig.class,
        ImporterConfig.class,
        IllConfig.class,
        IllViewConfig.class,
        MessageConfig.class,
        MessageViewConfig.class,
        MailLoanConfig.class,
        OaiProviderConfig.class,
        ObalkyknihConfig.class,
        ObalkyknihServiceConfig.class,
        PaymentCsobGwConfig.class,
        PaymentGopayConfig.class,
        PaymentGpwebpayConfig.class,
        PowerkeyConfig.class,
        ProvidedSeekingConfig.class,
        RecordBindingConfig.class,
        RecordExportConfig.class,
        RecordTableConfig.class,
        SeekingConfig.class,
        Sip2ServerConfig.class,
        SolSynchronizationConfig.class,
        SutinSynchronizationConfig.class,
        SynchronizationConfig.class,
        ThumbnailConfig.class,
        UserCoreConfig.class,
        VerbisboxUpdateConfig.class,
        VerbisboxItemInfoConfig.class,
        VerbisboxerNotificationConfig.class,
        VerbisboxerManagerConfig.class,
        UnisSynchronizationConfig.class,
        UnobSynchronizationConfig.class,
        UtilConfig.class,
        WalletConfig.class,
        ZiskejConfig.class,
        ZiskejIllConfig.class,
})
public class ModuleConfig {
}
