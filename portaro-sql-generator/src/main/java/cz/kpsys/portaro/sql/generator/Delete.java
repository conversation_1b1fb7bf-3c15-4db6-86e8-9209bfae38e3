package cz.kpsys.portaro.sql.generator;

import cz.kpsys.portaro.commons.util.StringUtil;

/**
 *
 * <AUTHOR>
 */
public class Delete extends QueryPart {

    private String tableName;

    public Delete(Query query) {
        super(query);
    }

    
    public void table(String tableName) {
        this.tableName = tableName;
    }
    
    
    
    @Override
    public String toString() {
        if (StringUtil.isNullOrEmpty(tableName)) {
            return "  !!!DELETE JE PRAZDNY!!!  ";
        }

        return "DELETE FROM " + tableName + " ";
    }

    
    
}
