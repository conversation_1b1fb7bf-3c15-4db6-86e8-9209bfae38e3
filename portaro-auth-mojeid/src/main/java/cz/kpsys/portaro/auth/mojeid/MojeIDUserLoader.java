package cz.kpsys.portaro.auth.mojeid;

import cz.kpsys.portaro.auth.MoreThanOneUserWithGivenIdentifierException;
import cz.kpsys.portaro.commons.object.repo.MoreThanOneItemFoundException;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.prop.UserServicePropertySearchProperty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

import static cz.kpsys.portaro.user.prop.MojeIDUserServicePropertiesConstants.MOJEID_ID;
import static cz.kpsys.portaro.user.prop.MojeIDUserServicePropertiesConstants.MOJEID_SERVICE;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MojeIDUserLoader {

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;

    public Optional<User> findUserByMojeIDId(@NonNull String id) {
        try {
            return userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.USER_SERVICE_PROP, List.of(UserServicePropertySearchProperty.withoutValidity(MOJEID_SERVICE, MOJEID_ID, id))));
        } catch (MoreThanOneItemFoundException e) {
            throw new MoreThanOneUserWithGivenIdentifierException(id);
        }
    }

    public Optional<User> findUserByOldOpenid2Id(@Nullable String id) {
        if (id == null) {
            return Optional.empty();
        }

        try {
            return userSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.OPENID, id));
        } catch (MoreThanOneItemFoundException e) {
            throw new MoreThanOneUserWithGivenIdentifierException(id);
        }
    }
}
