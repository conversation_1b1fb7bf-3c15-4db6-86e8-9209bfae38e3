package cz.kpsys.portaro.web.locale;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Locale;
import java.util.Optional;

public class RequestAttributeOptionalLocaleResolver implements OptionalLocaleResolver, LocaleSetter {

    public static final String ATTRIBUTE_NAME = RequestAttributeOptionalLocaleResolver.class.getName() + ".LOCALE";

    @Override
    public Optional<Locale> tryResolveLocale(HttpServletRequest request) {
        Locale attributeLocale = (Locale) request.getAttribute(ATTRIBUTE_NAME);

        if (attributeLocale != null) {
            return Optional.of(attributeLocale);
        }

        return Optional.empty();
    }

    @Override
    public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
        request.setAttribute(ATTRIBUTE_NAME, locale);
    }
}
