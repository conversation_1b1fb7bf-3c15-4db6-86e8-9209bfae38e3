package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.loan.LoanId;
import cz.kpsys.portaro.loan.ill.SeekingProvisionDeliveryChannel;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.time.Instant;
import java.util.UUID;

import static java.util.Objects.requireNonNull;

public record IllEntity(

        @NonNull
        Integer id,

        @NonNull
        Boolean active,

        @NonNull
        Integer stateId,

        @Nullable
        Integer documentKindedId,

        @NullableNotBlank
        String volume,

        @NullableNotBlank
        String page,

        @NullableNotBlank
        String article,

        @NullableNotBlank
        String note,

        @NonNull
        Boolean eventuallyPhotocopyable,

        @NonNull
        Boolean eventuallyReservable,

        @NonNull
        Boolean eventuallyOnSiteLendable,

        @NonNull
        Boolean eventuallyAbroadDeliverable,

        @Nullable
        Integer requesterUserId,

        @Nullable
        Integer seekerUserId,

        @Nullable
        Integer seekingLoanRealizationId,

        @Nullable
        UUID provisionId,

        @Nullable
        Integer provisionProviderUserId,

        @NonNull
        Integer departmentId,

        @NonNull
        SeekingProvisionDeliveryChannel seekingProvisionDeliveryChannel,

        @NonNull
        Instant createDate,

        @Nullable
        Instant commenceDate,

        @Nullable
        Instant activeProvisionSeekerActivateDate,

        @Nullable
        Instant activeProvisionProviderAcceptDate,

        @NullableNotBlank
        String activeProvisionProviderAcceptCondition,

        @Nullable
        Instant activeProvisionSeekerAcceptDate,

        @Nullable
        Instant activeProvisionProviderReserveDate,

        @Nullable
        Instant activeProvisionProviderSendDate,

        @Nullable
        Instant activeProvisionSeekerReceiveDate,

        @Nullable
        Instant activeProvisionSeekerSendDate,

        @Nullable
        Instant activeProvisionProviderReceiveDate,

        @Nullable
        Instant provisionSeekerCancelDate,

        @Nullable
        Instant provisionProviderCancelDate,

        @Nullable
        Instant requesterObtainDeadlineDate,

        @NullableNotBlank
        String seekerReferenceId,

        @NullableNotBlank
        String activeProvisionProviderReferenceId,

        @NullableNotBlank
        String exemplarIdentifier,

        @NullableNotBlank
        String exemplarSignature,

        @Nullable
        Integer activeProvisionLoanRealizationId,

        @NonNull
        Price price,

        @NullableNotBlank
        String ziskejTicketId,

        @NullableNotBlank
        String ziskejSubticketid

) implements IdentifiedRecord<Integer> {

    @NonNull
    public Boolean providersView() {
        return active();
    }

    @Nullable
    public Integer providersViewRequesterUserId() {
        return requesterUserId;
    }

    @NonNull
    public Integer seekersViewRequesterUserId() {
        return requireNonNull(requesterUserId, "Requester reader id (mvs.fk_uziv_cten) cannot be null on passive (seeker's) ILL");
    }

    @Nullable
    public Integer requesterUserId() {
        return providersView() ? providersViewRequesterUserId() : seekersViewRequesterUserId();
    }

    @NonNull
    public Integer seekersViewDocumentKindedId() {
        return requireNonNull(documentKindedId, () -> "Document id must be not null on passive (seeker's) ILL realization %s".formatted(id));
    }

    @NonNull
    public Integer providersViewDocumentKindedId() {
        return requireNonNull(documentKindedId, () -> "Document id must be not null on active (provider's) ILL realization %s".formatted(id));
    }

    @NonNull
    public Integer documentKindedId() {
        return providersView() ? providersViewDocumentKindedId() : seekersViewDocumentKindedId();
    }

    @NonNull
    public Integer providersViewSeekerUserId() {
        return requireNonNull(seekerUserId, () -> "Seeker library id (mvs.fk_uziv_knih) cannot be null on active (provider's) ILL request (id %s)".formatted(id));
    }

    @Nullable
    public Integer seekersViewSeekerUserId() {
        return seekerUserId;
    }

    @Nullable
    public Integer seekerUserId() {
        return providersView() ? providersViewSeekerUserId() : seekersViewSeekerUserId();
    }

    @Nullable
    public LoanId seekingLoanId() {
        Integer intLoanId = seekingLoanRealizationId;
        if (intLoanId == null) {
            return null;
        }
        return LoanId.ofNotSure(intLoanId);
    }

    @Nullable
    public Integer activeProvisionProviderId() {
        return provisionProviderUserId();
    }

    @Nullable
    public LoanId activeProvisionLoanId() {
        Integer intLoanId = activeProvisionLoanRealizationId;
        if (intLoanId == null) {
            return null;
        }
        return LoanId.ofNotSure(intLoanId);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, IllEntity.class);
    }

    @Override
    public int hashCode() {
        return getId().hashCode();
    }

}
