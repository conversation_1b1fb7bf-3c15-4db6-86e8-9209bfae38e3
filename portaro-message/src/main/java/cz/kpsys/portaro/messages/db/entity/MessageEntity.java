package cz.kpsys.portaro.messages.db.entity;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class MessageEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = DEPARTMENT_ID)
    @NonNull
    Integer departmentId;

    @NotNull
    @Column(name = MESSAGE_TOPIC_ID)
    Integer messageTopicId;

    @NotNull
    @Column(name = MESSAGE_SEVERITY_ID)
    String messageSeverityId;

    @NotNull
    @Column(name = SENDER_USER_ID)
    Integer senderUserId;

    @Nullable
    @Column(name = TARGET_USER_ID)
    Integer targetUserId;

    @NotNull
    @Column(name = CREATION_EVENT_ID)
    UUID creationEventId;

    @NotNull
    @Column(name = CONFIRMATION_NECESSARY)
    Boolean confirmationNecessary;

    @Nullable
    @Column(name = THREAD_RECORD_ID)
    UUID threadRecordId;

    @Nullable
    @Column(name = MESSAGE_CONTENT)
    String content;

    @Nullable
    @Column(name = CONTENT_TYPE)
    String contentType;

    @Nullable
    @Column(name = DIRECTORY_ID)
    Integer directoryId;
}
