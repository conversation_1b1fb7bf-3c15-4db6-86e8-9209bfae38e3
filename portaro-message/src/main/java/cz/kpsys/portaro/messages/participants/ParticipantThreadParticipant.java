package cz.kpsys.portaro.messages.participants;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

public record ParticipantThreadParticipant(

        @NonNull
        UUID id,

        @NonNull
        UUID thread,

        @NonNull
        UUID participantId,

        @NonNull
        Instant createDate,

        @Nullable
        UUID lastViewedMessageId

)  implements ThreadParticipant {
        @Override
        public boolean equals(Object obj) {
                return ObjectUtil.equalsIdentified(this, obj, ParticipantThreadParticipant.class);
        }

        @Override
        public int hashCode() {
                return Objects.hash(id);
        }
}
