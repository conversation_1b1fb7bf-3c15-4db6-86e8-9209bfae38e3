<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="portaro [bootRun] on test.kpsys.cz" type="GradleRunConfiguration" factoryName="Gradle" singleton="true">
    <ExternalSystemSettings>
      <option name="env">
        <map>
          <entry key="APPSERVER_URL" value="http://********:8184" />
          <entry key="PORTARO_INI__OPAC__ForceHttps" value="ANO" />
          <entry key="PORTARO_INI__OPAC__URL" value="http://localhost" />
          <entry key="PORTARO_INI__integ_obalkyknih_enabled" value="false" />
          <entry key="PORTARO_RESOURCESUPDATE_STRICT" value="true" />
          <entry key="xPORTARO_INI__record_detail_portaroLoad" value="false" />
        </map>
      </option>
      <option name="executionName" />
      <option name="externalProjectPath" value="$PROJECT_DIR$/portaro-runner" />
      <option name="externalSystemIdString" value="GRADLE" />
      <option name="scriptParameters" value="-Penv=develop --warning-mode=all --stacktrace -Ptested" />
      <option name="taskDescriptions">
        <list />
      </option>
      <option name="taskNames">
        <list>
          <option value="bootRun" />
        </list>
      </option>
      <option name="vmOptions" />
    </ExternalSystemSettings>
    <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
    <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
    <DebugAllEnabled>false</DebugAllEnabled>
    <RunAsTest>false</RunAsTest>
    <method v="2" />
  </configuration>
</component>