package cz.kpsys.portaro.sip2.server.impl;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.setting.SettingKey;
import lombok.NonNull;

import java.util.List;

public class Sip2ServerSettingKeys {

    public static final String SECTION_SIP2 = "sip2";
    public static final String SECTION_SIP2_USER = "sip2.user";
    public static final String SECTION_SIP2_ITEMINFO = "sip2.itemInfo";
    public static final String SECTION_SIP2_LENDING = "sip2.lending";
    public static final String SECTION_SIP2_RETURNING = "sip2.returning";
    public static final String SECTION_SIP2_RENEWING = "sip2.renewing";

    public static final SettingKey<@NonNull Boolean> SIP2_ENABLED = new SettingKey<>(SECTION_SIP2, "enabled");
    public static final SettingKey<@NonNull Integer> SIP2_PORT = new SettingKey<>(SECTION_SIP2, "port");
    public static final SettingKey<@NullableNotBlank String> SIP2_TERMINAL_PASSWORD = new SettingKey<>(SECTION_SIP2, "terminalPassword");
    public static final SettingKey<Integer> SIP2_CONTEXT = new SettingKey<>(SECTION_SIP2, "context");
    public static final SettingKey<@NullableNotBlank String> SIP2_INSTITUTION_ID = new SettingKey<>(SECTION_SIP2, "institution");
    public static final SettingKey<@NonNull String> SIP2_USER_IDENTIFIER = new SettingKey<>(SECTION_SIP2_USER, "identifier");
    public static final SettingKey<@NonNull Boolean> SIP2_STRICT_USER_IDENTIFIER_ENABLED = new SettingKey<>(SECTION_SIP2_USER, "strictIdentifierEnabled");
    public static final SettingKey<@NonNull List<String>> SIP2_FORBIDDEN_LENDING_READER_CATEGORIES = new SettingKey<>(SECTION_SIP2_LENDING, "forbiddenReaderCategories");
    public static final SettingKey<@NonNull Boolean> SIP2_ALLOWED_LENDING_LOCATIONS_ENABLED = new SettingKey<>(SECTION_SIP2_LENDING, "allowedLocationsEnabled");
    public static final SettingKey<@NonNull List<Integer>> SIP2_ALLOWED_LENDING_LOCATIONS = new SettingKey<>(SECTION_SIP2_LENDING, "allowedLocations");
    public static final SettingKey<@NonNull List<Integer>> SIP2_FORBIDDEN_LENDING_LOCATIONS = new SettingKey<>(SECTION_SIP2_LENDING, "forbiddenLocations");
    public static final SettingKey<@NonNull List<String>> SIP2_FORBIDDEN_LOAN_CATEGORIES = new SettingKey<>(SECTION_SIP2_LENDING, "forbiddenLoanCategories");
    public static final SettingKey<@NonNull Boolean> SIP2_LENDING_EXEMPLAR_MAX_PRICE_ENABLED = new SettingKey<>(SECTION_SIP2_LENDING, "maxPriceEnabled");
    public static final SettingKey<Integer> SIP2_LENDING_EXEMPLAR_MAX_PRICE = new SettingKey<>(SECTION_SIP2_LENDING, "maxPrice");
    public static final SettingKey<@NonNull Boolean> SIP2_ALLOWED_LENDING_EXEMPLAR_STATUSES_ENABLED = new SettingKey<>(SECTION_SIP2_LENDING, "allowedExemplarStatusesEnabled");
    public static final SettingKey<@NonNull List<Integer>> SIP2_ALLOWED_LENDING_EXEMPLAR_STATUSES = new SettingKey<>(SECTION_SIP2_LENDING, "allowedExemplarStatuses");
    public static final SettingKey<@NonNull Boolean> SIP2_HOLD_PATRON_ID_IN_ITEM_PROPERTIES_ENABLED = new SettingKey<>(SECTION_SIP2_ITEMINFO, "holdPatronIdInItemPropertiesEnabled");
    public static final SettingKey<@NonNull Boolean> SIP2_LOAN_MEDIUM_IN_ITEM_PROPERTIES_ENABLED = new SettingKey<>(SECTION_SIP2_ITEMINFO, "loanMediumInItemPropertiesEnabled");
    public static final SettingKey<@NonNull Boolean> SIP2_BOOKED_RECORDS_RETURNING_ENABLED = new SettingKey<>(SECTION_SIP2_RETURNING, "bookedRecordsReturningEnabled");
    public static final SettingKey<@NonNull Boolean> SIP2_SORT_BIN_PROCEDURE_ENABLED = new SettingKey<>(SECTION_SIP2_RETURNING, "sortBinIdProcedureEnabled");
    public static final SettingKey<@NonNull Boolean> SIP2_PATRON_INFORMATION_TRIGGER_RENEWAL_ENABLED = new SettingKey<>(SECTION_SIP2_RENEWING, "patronInformationTriggerRenewal");

}