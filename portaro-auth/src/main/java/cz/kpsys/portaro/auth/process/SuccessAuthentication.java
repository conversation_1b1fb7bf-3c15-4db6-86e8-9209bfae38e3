package cz.kpsys.portaro.auth.process;

import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.AuthableUser;
import lombok.NonNull;

public interface SuccessAuthentication extends BasicUserAuthentication {

    AuthableUser getActiveUser();

    /**
     * Returns department, where user was authenticated
     */
    @NonNull
    Department getCtx();

}
