package cz.kpsys.portaro.ext.wallet;

import cz.kpsys.portaro.user.UserStringGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WalletConfig {

    @NonNull UserStringGenerator prettyUserNameGenerator;

    @Bean
    public AppleWalletCardCreator appleWalletCardCreator() {
        return new AppleWalletCardCreator(
                prettyUserNameGenerator
        );
    }

    @Bean
    public GoogleWalletCardCreator googleWalletCardCreator() {
        return new GoogleWalletCardCreator(
                prettyUserNameGenerator
        );
    }

    @Bean
    public WalletApiController walletApiController() {
        return new WalletApiController(
                appleWalletCardCreator(),
                googleWalletCardCreator()
        );
    }
}
