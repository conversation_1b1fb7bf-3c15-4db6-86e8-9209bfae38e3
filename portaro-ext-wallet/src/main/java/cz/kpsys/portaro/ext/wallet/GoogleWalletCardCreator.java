package cz.kpsys.portaro.ext.wallet;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.role.reader.ReaderRole;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.io.FileInputStream;
import java.net.URI;
import java.security.interfaces.RSAPrivateKey;
import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GoogleWalletCardCreator implements WalletCardCreator<URI> {

    // TODO: Use proper values
    private static final String SERVICE_ACCOUNT_FILE = "/path/to/service-account.json";
    private static final String ISSUER_ID = "your-issuer-id"; // e.g., "issuer.systemist"
    private static final String CLASS_SUFFIX = "library_card";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public URI generateWalletCard(Person person, Department ctx) throws Exception {
        ReaderRole readerRole = person.roleStreamOn(ReaderRole.class, ctx)
                .findFirst()
                .orElseThrow();

        String passId = UUID.randomUUID().toString();
        String barcodeValue = readerRole.getBarCode();

        // Load service account credentials
        GoogleCredentials credentials = GoogleCredentials.fromStream(new FileInputStream(SERVICE_ACCOUNT_FILE));

        if (!(credentials instanceof ServiceAccountCredentials serviceAccountCredentials)) {
            throw new IllegalStateException("Credentials are not a service account");
        }

        RSAPrivateKey privateKey = (RSAPrivateKey) serviceAccountCredentials.getPrivateKey();
        String serviceAccountEmail = serviceAccountCredentials.getClientEmail();

        // Define the pass object
        Map<String, Object> passObject = new LinkedHashMap<>();
        passObject.put("id", passId);
        passObject.put("classId", ISSUER_ID + "." + CLASS_SUFFIX);
        passObject.put("state", "ACTIVE");

        /* TODO: Put image here
        passObject.put("heroImage", Map.of(
                "sourceUri", Map.of("uri", user.getClientLogoUrl())
        ));
        */

        passObject.put("textModulesData", List.of(
                Map.of("header", "Library Member", "body", person.getUsername())
        ));

        passObject.put("barcode", Map.of(
                "type", "CODE_128",
                "value", barcodeValue,
                "alternateText", "Verbis Entry"
        ));

        // JWT payload
        Map<String, Object> payload = new LinkedHashMap<>();
        payload.put("iss", serviceAccountEmail);
        payload.put("aud", "google");
        payload.put("typ", "savetowallet");
        payload.put("payload", Map.of("genericObjects", List.of(passObject)));

        // Sign JWT
        Algorithm algorithm = Algorithm.RSA256(null, privateKey);
        String token = JWT.create()
                .withClaim("iss", (String) payload.get("iss"))
                .withClaim("aud", (String) payload.get("aud"))
                .withClaim("typ", (String) payload.get("typ"))
                .withClaim("payload", objectMapper.convertValue(payload.get("payload"), Map.class))
                .sign(algorithm);

        // Return Add to Google Wallet URL
        return URI.create("https://pay.google.com/gp/v/save/" + token);
    }
}
