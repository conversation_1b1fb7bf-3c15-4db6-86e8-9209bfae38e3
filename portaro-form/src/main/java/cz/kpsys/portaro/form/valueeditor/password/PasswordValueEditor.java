package cz.kpsys.portaro.form.valueeditor.password;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.form.valueeditor.LocalizationsAwareValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditorValidations;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.BasicValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorType;
import lombok.NonNull;
import lombok.Value;
import lombok.With;

import java.util.Map;
import java.util.Optional;

@With
@Value
public class PasswordValueEditor implements LocalizationsAwareValueEditor<PasswordValueEditorOptions, TextValueEditorValidations, PasswordValueEditor> {

    public static PasswordValueEditor getEmptyEditor() {
        return new PasswordValueEditor(BasicValueEditorType.PASSWORD, null, null, null, null, null, null, null, null);
    }

    @NonNull
    ValueEditorType type;

    String editorId;

    String editorName;

    Text placeholder;

    Boolean disabled;

    @JsonProperty("isVisible")
    Boolean visible;

    PasswordValueEditorOptions options;

    TextValueEditorValidations validations;

    Map<String, Text> localizations;

    public Optional<String> getEditorId() {
        return Optional.ofNullable(editorId);
    }

    public Optional<String> getEditorName() {
        return Optional.ofNullable(editorName);
    }

    public Optional<Text> getPlaceholder() {
        return Optional.ofNullable(placeholder);
    }

    @Override
    public Optional<Boolean> getDisabled() {
        return Optional.ofNullable(disabled);
    }

    @Override
    public Optional<Boolean> getVisible() {
        return Optional.ofNullable(visible);
    }

    public Optional<PasswordValueEditorOptions> getOptions() {
        return Optional.ofNullable(options);
    }

    public Optional<TextValueEditorValidations> getValidations() {
        return Optional.ofNullable(validations);
    }

    public Optional<Map<String, Text>> getLocalizations() {
        return Optional.ofNullable(localizations);
    }

    @Override
    public PasswordValueEditor addLocalization(String messageKey, Text localization) {
        return LocalizationsAwareValueEditor.addLocalization(this, messageKey, localization);
    }

    /* --- */

    @Override
    public PasswordValueEditor withRequired(boolean required) {
        return this.withValidations(this.getValidations().orElseGet(TextValueEditorValidations::getEmptyValidations).withRequired(required));
    }

    public PasswordValueEditor withMaxLength(Integer maxLength) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(TextValueEditorValidations::getEmptyValidations)
                        .withMaxlengthProvider(StaticProvider.of(maxLength))
        );
    }

    public PasswordValueEditor withMaxLength(Provider<Integer> maxLengthProvider) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(TextValueEditorValidations::getEmptyValidations)
                        .withMaxlengthProvider(maxLengthProvider)
        );
    }

    public PasswordValueEditor withConfirmation(Boolean confirmation) {
        return this.withOptions(
                this.getOptions()
                        .orElseGet(PasswordValueEditorOptions::getEmptyOptions)
                        .withWithConfirmation(confirmation)
        );
    }

    public PasswordValueEditor withPattern(String pattern) {
        return this.withValidations(
                this.getValidations()
                        .orElseGet(TextValueEditorValidations::getEmptyValidations)
                        .withPattern(pattern)
        );
    }
}
