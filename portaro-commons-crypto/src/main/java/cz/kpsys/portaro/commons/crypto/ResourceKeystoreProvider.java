package cz.kpsys.portaro.commons.crypto;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;

import java.io.InputStream;
import java.security.KeyStore;

@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class ResourceKeystoreProvider implements Provider<@NonNull KeyStore> {

    @NonNull Provider<@NonNull Resource> resourceProvider;
    @NonNull String keystoreType;
    @NonNull String keystorePassword;

    public static ResourceKeystoreProvider ofStatic(@NonNull Resource resource, @NonNull String keystoreType, @NonNull String keystorePassword) {
        return new ResourceKeystoreProvider(StaticProvider.of(resource), keystoreType, keystorePassword);
    }

    public static ResourceKeystoreProvider ofJks(@NonNull Provider<@NonNull Resource> resource, @NonNull String keystorePassword) {
        return new ResourceKeystoreProvider(resource, KeystoreConstants.KEYSTORE_TYPE_JKS, keystorePassword);
    }

    public static ResourceKeystoreProvider ofJceks(@NonNull Provider<@NonNull Resource> resource, @NonNull String keystorePassword) {
        return new ResourceKeystoreProvider(resource, KeystoreConstants.KEYSTORE_TYPE_JCEKS, keystorePassword);
    }

    @NonNull
    @Override
    public KeyStore get() {
        Resource resource = resourceProvider.get();

        log.info("Loading keystore of type {} from {}", keystoreType, resource);

        try (InputStream is = resource.getInputStream()) {
            KeyStore keystore = KeyStore.getInstance(keystoreType);
            keystore.load(is, keystorePassword.toCharArray());
            return keystore;

        } catch (Exception ex) {
            throw new RuntimeException("Cannot load keystore of type %s from %s".formatted(keystoreType, resource), ex);
        }
    }
}
