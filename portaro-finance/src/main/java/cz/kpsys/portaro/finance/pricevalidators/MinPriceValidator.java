package cz.kpsys.portaro.finance.pricevalidators;

import cz.kpsys.portaro.finance.Price;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.constraints.Min;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.math.BigDecimal;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MinPriceValidator implements ConstraintValidator<Min, Price> {

    @NonFinal BigDecimal minValue;

    @Override
    public void initialize(Min constraintAnnotation) {
        minValue = BigDecimal.valueOf(constraintAnnotation.value());
    }

    @Override
    public boolean isValid(Price price, ConstraintValidatorContext context) {
        if (price == null) {
            return true;
        }

        return price.amount().compareTo(minValue) >= 0;  // amount >= minValue
    }
}
