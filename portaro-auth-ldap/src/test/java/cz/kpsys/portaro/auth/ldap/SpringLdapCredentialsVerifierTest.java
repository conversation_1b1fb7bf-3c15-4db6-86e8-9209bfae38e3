package cz.kpsys.portaro.auth.ldap;

import cz.kpsys.portaro.auth.BadLoginCredentialsException;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.department.Department;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.ldap.AuthenticationException;

import java.util.List;

public class SpringLdapCredentialsVerifierTest {

    private static final String LDAP_LOCALHOST_URL = "ldaps://localhost:8636";
    private static final String VFU_CONTEXT_CONNECT_USERNAME = "<EMAIL>";
    private static final String VFU_CONTEXT_CONNECT_PASSWORD = "V9f3B1r7";
    private static final String VFU_STUDENT_USERNAME = "kpsys_test1";
    private static final String VFU_STUDENT_PASSWORD = "aWjA63Vk";
    private static final String VFU_STUDENT_INVALID_USERNAME = "kpsys_invalid";
    private static final String VFU_STUDENT_INVALID_PASSWORD = "invalid";
    private static final String VFU_EMPLOYEE_USERNAME = "kpsys_test2";
    private static final String VFU_EMPLOYEE_PASSWORD = "yQRjN4Qn";

    @Test
    public void shouldSuccessfulyAuthenticate_WhenValidCredentials() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of("ldap://ldap.forumsys.com"),
                ContextIgnoringContextualProvider.of("uid={0}"),
                ContextIgnoringContextualProvider.of(List.of("dc=example,dc=com")),
                ContextIgnoringContextualProvider.<Department, String>ofNull().optionally(),
                ContextIgnoringContextualProvider.<Department, String>ofNull().optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(false)
        );
        ldapCredentialsVerifier.verify(Department.testingRoot(), "tesla", "password");
    }

    @Test
    public void shouldFailAuthenticate_WhenInvalidCredentials() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of("ldap://ldap.forumsys.com"),
                ContextIgnoringContextualProvider.of("uid={0}"),
                ContextIgnoringContextualProvider.of(List.of("dc=example,dc=com")),
                ContextIgnoringContextualProvider.<Department, String>ofNull().optionally(),
                ContextIgnoringContextualProvider.<Department, String>ofNull().optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(false)
        );
        Assertions.assertThrows(BadLoginCredentialsException.class, () -> ldapCredentialsVerifier.verify(Department.testingRoot(), "tesla", "wrongpassword"));
    }

    @Test
    @Disabled("Only when opened ssh tunnel to VFU")
    public void shouldSuccessfulyAuthenticateEmp_WhenValidCredentialsOnVfuViaLocalhost() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("sAMAccountName={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=Zamestnanci,dc=vfu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_USERNAME).optionally(),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_PASSWORD).optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        ldapCredentialsVerifier.verify(Department.testingRoot(), VFU_EMPLOYEE_USERNAME, VFU_EMPLOYEE_PASSWORD);
    }

    @Test
    @Disabled("Only when opened ssh tunnel to VFU")
    public void shouldSuccessfulyAuthenticateStudent_WhenValidCredentialsOnVfuViaLocalhost() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("sAMAccountName={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=studenti,dc=vfu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_USERNAME).optionally(),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_PASSWORD).optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        ldapCredentialsVerifier.verify(Department.testingRoot(), VFU_STUDENT_USERNAME, VFU_STUDENT_PASSWORD);
    }

    @Test
    @Disabled("Only when opened ssh tunnel to VFU")
    public void shouldSuccessfulyAuthenticateStudent_WhenValidCredentialsOnVfuViaLocalhostWithTwoBaseDNs() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("sAMAccountName={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=Zamestnanci,dc=vfu,dc=cz", "ou=studenti,dc=vfu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_USERNAME).optionally(),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_PASSWORD).optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        ldapCredentialsVerifier.verify(Department.testingRoot(), VFU_STUDENT_USERNAME, VFU_STUDENT_PASSWORD);
    }

    @Test
    @Disabled("Only when opened ssh tunnel to VFU")
    public void shouldThrowBadLoginCredentialsException_WhenInValidPasswordOnVfuViaLocalhost() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("sAMAccountName={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=studenti,dc=vfu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_USERNAME).optionally(),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_PASSWORD).optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        Assertions.assertThrows(BadLoginCredentialsException.class, () -> ldapCredentialsVerifier.verify(Department.testingRoot(), VFU_STUDENT_USERNAME, VFU_STUDENT_INVALID_PASSWORD));
    }

    @Test
    @Disabled("Only when opened ssh tunnel to VFU")
    public void shouldThrowBadLoginCredentialsException_WhenInValidUsernameOnVfuViaLocalhost() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("sAMAccountName={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=studenti,dc=vfu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_USERNAME).optionally(),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_PASSWORD).optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        Assertions.assertThrows(BadLoginCredentialsException.class, () -> ldapCredentialsVerifier.verify(Department.testingRoot(), VFU_STUDENT_INVALID_USERNAME, VFU_STUDENT_PASSWORD));
    }

    @Test
    @Disabled("Only when opened ssh tunnel to VFU")
    public void shouldThrowAuthenticationException_WhenInValidCredentialsOfContextConnectUserOnVfuViaLocalhost() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("sAMAccountName={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=studenti,dc=vfu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>of(VFU_CONTEXT_CONNECT_USERNAME).optionally(),
                ContextIgnoringContextualProvider.<Department, String>of("invalid").optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        Assertions.assertThrows(AuthenticationException.class, () -> ldapCredentialsVerifier.verify(Department.testingRoot(), VFU_STUDENT_USERNAME, VFU_STUDENT_PASSWORD));
    }

    @Test
    @Disabled("Only when opened ssh tunnel to MZLU")
    public void shouldSuccessfulyAuthenticateStudent_WhenValidCredentialsOnMendeluViaLocalhost() {
        LdapCredentialsVerifier ldapCredentialsVerifier = new SpringLdapCredentialsVerifier(
                ContextIgnoringContextualProvider.of(LDAP_LOCALHOST_URL),
                ContextIgnoringContextualProvider.of("uid={0}"),
                ContextIgnoringContextualProvider.of(List.of("ou=People,dc=mendelu,dc=cz")),
                ContextIgnoringContextualProvider.<Department, String>ofNull().optionally(),
                ContextIgnoringContextualProvider.<Department, String>ofNull().optionally(),
                ContextIgnoringContextualProvider.of(false),
                ContextIgnoringContextualProvider.of(true)
        );
        ldapCredentialsVerifier.verify(Department.testingRoot(), "qqpachol", "Pardubice12");
    }

}