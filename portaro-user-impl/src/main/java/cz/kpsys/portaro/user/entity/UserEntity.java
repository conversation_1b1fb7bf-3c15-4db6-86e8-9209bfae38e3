package cz.kpsys.portaro.user.entity;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.validation.constraints.Min;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.UUID;

public record UserEntity(

        @NonNull
        @Min(1)
        Integer id,

        @Nullable
        UUID rid,

        @NullableNotBlank
        String nativeName,

        @NonNull
        UUID creationEventId,

        @Nullable
        UUID activationEventId,

        @Nullable
        UUID anonymizationEventId,

        @Nullable
        UUID deletionEventId,

        @NullableNotBlank
        String username,

        @NullableNotBlank
        String passwordHash,

        @NullableNotBlank
        String syncId,

        @Nullable
        UUID recordId,

        @Nullable
        UserAuthorityEntity userAuthority,

        @Nullable
        PersonEntity person,

        @Nullable
        InstitutionEntity institution,

        @Nullable
        LibraryEntity library,

        @Nullable
        SoftwareEntity software,

        @Nullable
        ReaderAccountEntity readerAccount,

        @Nullable
        EditorAccountEntity editorAccount,

        @Nullable
        SupplierAccountEntity supplierAccount

) {}
