package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.user.entity.UserEntity;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4;
import static cz.kpsys.portaro.databasestructure.UserDb.*;
import static cz.kpsys.portaro.user.UserLoaderConstants.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbAllUsersByRidsLoader implements AllByIdsLoadable<User, UUID> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Converter<List<? extends UserEntity>, @NonNull List<User>> entitiesToUsersConverter;

    @NonNull RowMapper<UserEntity> rowMapper;

    @Override
    public List<User> getAllByIds(@NonNull List<UUID> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = getQuery(ids);
        List<UserEntity> userEntities = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), rowMapper);

        List<User> users = entitiesToUsersConverter.convert(userEntities);

        log.debug("Successfully loaded and mapped {} users by {} ids ({})", users.size(), ids.size(), ids);

        users.sort(ComparatorForExplicitIdSorting.forRIdentified(ids));
        return users;
    }

    @NonNull
    private SelectQuery getQuery(List<UUID> ids) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                AS(TC(UZIVATELE.TABLE, UZIVATELE.ID), UZIVATELE__ID),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV), UZIVATELE__ID_UZIV),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.ZOBR_JMENO_JP), UZIVATELE__ZOBR_JMENO_JP),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.USERNAME), UZIVATELE__USERNAME),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.PWD), UZIVATELE__PASSWORD),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.SYNC_ID), UZIVATELE__SYNC_ID),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.DATCAS_ZALOZENI), UZIVATELE__DATCAS_ZALOZENI),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.CREATION_EVENT_ID), UZIVATELE__CREATION_EVENT_ID),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.ACTIVATION_EVENT_ID), UZIVATELE__ACTIVATION_EVENT_ID),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.ANONYMIZATION_EVENT_ID), UZIVATELE__ANONYMIZATION_EVENT_ID),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.DELETION_EVENT_ID), UZIVATELE__DELETION_EVENT_ID),
                AS(TC(UZIVATELE.TABLE, UZIVATELE.RECORD_ID), UZIVATELE__RECORD_ID),

                AS(TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID), KATAUT_4__RECORD_ID),
                AS(TC(KATAUT_4.TABLE, KATAUT_4.ID_AUT), KATAUT_4__ID_AUT),
                AS(TC(KATAUT_4.TABLE, KATAUT_4.NAZEV), KATAUT_4__NAZEV),

                AS(TC(OSOBY.TABLE, OSOBY.FK_UZIV), OSOBY__FK_UZIV),
                AS(TC(OSOBY.TABLE, OSOBY.JMENO), OSOBY__JMENO),
                AS(TC(OSOBY.TABLE, OSOBY.MIDDLE_NAME), OSOBY__MIDDLE_NAME),
                AS(TC(OSOBY.TABLE, OSOBY.PRIJMENI), OSOBY__PRIJMENI),
                AS(TC(OSOBY.TABLE, OSOBY.NAME_SOURCE), OSOBY__NAME_SOURCE),
                AS(TC(OSOBY.TABLE, OSOBY.TITUL), OSOBY__TITUL),
                AS(TC(OSOBY.TABLE, OSOBY.TITUL_ZA), OSOBY__TITUL_ZA),
                AS(TC(OSOBY.TABLE, OSOBY.DEGREE_SOURCE), OSOBY__DEGREE_SOURCE),
                AS(TC(OSOBY.TABLE, OSOBY.GENDER), OSOBY__GENDER),
                AS(TC(OSOBY.TABLE, OSOBY.GENDER_SOURCE), OSOBY__GENDER_SOURCE),
                AS(TC(OSOBY.TABLE, OSOBY.OPENID), OSOBY__OPENID),
                AS(TC(OSOBY.TABLE, OSOBY.OSOBA_GUID), OSOBY__OSOBA_GUID),
                AS(TC(OSOBY.TABLE, OSOBY.NET_ID), OSOBY__NET_ID),
                AS(TC(OSOBY.TABLE, OSOBY.DAT_NAR), OSOBY__DAT_NAR),
                AS(TC(OSOBY.TABLE, OSOBY.DEATH_DATE), OSOBY__DEATH_DATE),
                AS(TC(OSOBY.TABLE, OSOBY.LIFE_DATE_SOURCE), OSOBY__LIFE_DATE_SOURCE),
                AS(TC(OSOBY.TABLE, OSOBY.ZAMESTNANI), OSOBY__ZAMESTNANI),
                AS(TC(OSOBY.TABLE, OSOBY.ADR_ZAM), OSOBY__ADR_ZAM),
                AS(TC(OSOBY.TABLE, OSOBY.FUNKCE), OSOBY__FUNKCE),
                AS(TC(OSOBY.TABLE, OSOBY.VZDELANI), OSOBY__VZDELANI),
                AS(TC(OSOBY.TABLE, OSOBY.TRIDA), OSOBY__TRIDA),
                AS(TC(OSOBY.TABLE, OSOBY.FORMA_STUDIA), OSOBY__FORMA_STUDIA),
                AS(TC(OSOBY.TABLE, OSOBY.CISOP), OSOBY__CISOP),
                AS(TC(OSOBY.TABLE, OSOBY.ICO), OSOBY__ICO),
                AS(TC(OSOBY.TABLE, OSOBY.DIC), OSOBY__DIC),
                AS(TC(OSOBY.TABLE, OSOBY.VZTAH_UNIV), OSOBY__VZTAH_UNIV),
                AS(TC(OSOBY.TABLE, OSOBY.MOJEID_POSL_UPDATE_TIME), OSOBY__MOJEID_POSL_UPDATE_TIME),
                AS(TC(OSOBY.TABLE, OSOBY.MOJEID_JE_VALID), OSOBY__MOJEID_JE_VALID),
                AS(TC(OSOBY.TABLE, OSOBY.BAKALARI), OSOBY__BAKALARI),
                AS(TC(OSOBY.TABLE, OSOBY.SOL_ID), OSOBY__SOL_ID),

                AS(TC(INSTITUCE.TABLE, INSTITUCE.FK_UZIV), INSTITUCE__FK_UZIV),
                AS(TC(INSTITUCE.TABLE, INSTITUCE.NAZEV), INSTITUCE__NAZEV),
                AS(TC(INSTITUCE.TABLE, INSTITUCE.OBCH_REJSTRIK), INSTITUCE__OBCH_REJSTRIK),
                AS(TC(INSTITUCE.TABLE, INSTITUCE.ICO), INSTITUCE__ICO),
                AS(TC(INSTITUCE.TABLE, INSTITUCE.DIC), INSTITUCE__DIC),
                AS(TC(INSTITUCE.TABLE, INSTITUCE.TYP_INSTITUCE), INSTITUCE__TYP_INSTITUCE),
                AS(TC(INSTITUCE.TABLE, INSTITUCE.HOMEPAGE_URL), INSTITUCE__HOMEPAGE_URL),

                AS(TC(SOFTWARE.TABLE, SOFTWARE.FK_UZIV), SOFTWARE__FK_UZIV),
                AS(TC(SOFTWARE.TABLE, SOFTWARE.NAZEV), SOFTWARE__NAZEV),
                AS(TC(SOFTWARE.TABLE, SOFTWARE.WEB_CRAWLER), SOFTWARE__WEB_CRAWLER),

                AS(TC(CTENARI.TABLE, CTENARI.FK_UZIV), CTENARI__FK_UZIV),
                AS(TC(CTENARI.TABLE, CTENARI.JE_POVOL), CTENARI__JE_POVOL),
                AS(TC(CTENARI.TABLE, CTENARI.JE_BLOKOVAN), CTENARI__JE_BLOKOVAN),
                AS(TC(CTENARI.TABLE, CTENARI.FK_CTENKAT), CTENARI__FK_CTENKAT),
                AS(TC(CTENARI.TABLE, CTENARI.FK_PUJC), CTENARI__FK_PUJC),
                AS(TC(CTENARI.TABLE, CTENARI.DAT_REG), CTENARI__DAT_REG),
                AS(TC(CTENARI.TABLE, CTENARI.KON_REG), CTENARI__KON_REG),
                AS(TC(CTENARI.TABLE, CTENARI.DATP_REG), CTENARI__DATP_REG),
                AS(TC(CTENARI.TABLE, CTENARI.DAT_OBNOV), CTENARI__DAT_OBNOV),
                AS(TC(CTENARI.TABLE, CTENARI.KOD_STAVU), CTENARI__KOD_STAVU),
                AS(TC(CTENARI.TABLE, CTENARI.TYP_TISK_REZE), CTENARI__TYP_TISK_REZE),
                AS(TC(CTENARI.TABLE, CTENARI.TYP_TISK_UPOM), CTENARI__TYP_TISK_UPOM),
                AS(TC(CTENARI.TABLE, CTENARI.BAR_COD), CTENARI__BAR_COD),
                AS(TC(CTENARI.TABLE, CTENARI.CIS_LEG), CTENARI__CIS_LEG),
                AS(TC(CTENARI.TABLE, CTENARI.POZNAMKA), CTENARI__POZNAMKA),
                AS(TC(CTENARI.TABLE, CTENARI.VZKAZ), CTENARI__VZKAZ),
                AS(TC(CTENARI.TABLE, CTENARI.RFID_UID), CTENARI__RFID_UID),

                AS(TC(KNIHOVNICI.TABLE, KNIHOVNICI.FK_UZIV), KNIHOVNICI__FK_UZIV),
                AS(TC(KNIHOVNICI.TABLE, KNIHOVNICI.UROVEN), KNIHOVNICI__UROVEN),
                AS(TC(KNIHOVNICI.TABLE, KNIHOVNICI.FK_GROUP), KNIHOVNICI__FK_GROUP),
                AS(TC(KNIHOVNICI.TABLE, KNIHOVNICI.JE_POVOL), KNIHOVNICI__JE_POVOL),
                AS(TC(KNIHOVNICI.TABLE, KNIHOVNICI.JE_SERVIS_WWW), KNIHOVNICI__JE_SERVIS_WWW),
                AS(TC(KNIHOVNICI.TABLE, KNIHOVNICI.VAL_KOD), KNIHOVNICI__VAL_KOD_WWW),

                AS(TC(KNIHOVNY.TABLE, KNIHOVNY.FK_UZIV), KNIHOVNY__FK_UZIV),
                AS(TC(KNIHOVNY.TABLE, KNIHOVNY.SIGLA), KNIHOVNY__SIGLA),
                AS(TC(KNIHOVNY.TABLE, KNIHOVNY.FK_PUJC), KNIHOVNY__FK_PUJC),

                AS(TC(DODAVATELE.TABLE, DODAVATELE.FK_UZIV), DODAVATELE__FK_UZIV)
        );
        sq.from(UZIVATELE.TABLE);
        sq.joins()
                .addLeft(KATAUT_4.TABLE, COLSEQ(TC(KATAUT_4.TABLE, KATAUT_4.FK_UZIV_KORP), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)))
                .addLeft(OSOBY.TABLE, COLSEQ(TC(OSOBY.TABLE, OSOBY.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)))
                .addLeft(INSTITUCE.TABLE, COLSEQ(TC(INSTITUCE.TABLE, INSTITUCE.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)))
                .addLeft(SOFTWARE.TABLE, COLSEQ(TC(SOFTWARE.TABLE, SOFTWARE.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)))
                .addLeft(CTENARI.TABLE, COLSEQ(TC(CTENARI.TABLE, CTENARI.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)) + AND + COLSEQ(TC(CTENARI.TABLE, CTENARI.JE_POVOL), sq.nextParamPlaceholder(true)))
                .addLeft(KNIHOVNICI.TABLE, COLSEQ(TC(KNIHOVNICI.TABLE, KNIHOVNICI.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)) + AND + COLSEQ(TC(KNIHOVNICI.TABLE, KNIHOVNICI.JE_POVOL), sq.nextParamPlaceholder(true)))
                .addLeft(KNIHOVNY.TABLE, COLSEQ(TC(KNIHOVNY.TABLE, KNIHOVNY.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)))
                .addLeft(DODAVATELE.TABLE, COLSEQ(TC(DODAVATELE.TABLE, DODAVATELE.FK_UZIV), TC(UZIVATELE.TABLE, UZIVATELE.ID_UZIV)));
        sq.where().in(TC(UZIVATELE.TABLE, UZIVATELE.ID), ids);
        return sq;
    }
}
