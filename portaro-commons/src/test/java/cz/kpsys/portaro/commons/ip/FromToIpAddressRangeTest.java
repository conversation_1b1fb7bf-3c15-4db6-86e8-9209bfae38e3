package cz.kpsys.portaro.commons.ip;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;

class FromToIpAddressRangeTest {

    @Test
    void shouldResolve() {
        FromToIpAddressRange range = new FromToIpAddressRange(new Ipv4Address("************"), new Ipv4Address("**************"));
        assertTrue(range.isWithin(new Ipv4Address("************")));
    }
}