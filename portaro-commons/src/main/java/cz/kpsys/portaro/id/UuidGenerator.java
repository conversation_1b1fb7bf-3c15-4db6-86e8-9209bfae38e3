package cz.kpsys.portaro.id;

import com.fasterxml.uuid.Generators;
import cz.kpsys.portaro.commons.util.StringUtil;

import java.nio.ByteBuffer;
import java.util.UUID;

public class UuidGenerator {

    public static final int UUID_LENGTH = 36;
    public static final int UUID_WITHOUT_DASHES_LENGTH = 32;
    public static final String UUID_PATTERN_INFIX = "[0-9a-f]{8}-[0-9a-f]{4}-[1-7][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}";
    public static final String UUID_PATTERN_WHOLE = "^" + UUID_PATTERN_INFIX + "$";

    public static UUID forIdentifier() {
        return Generators.timeBasedEpochGenerator().generate();
    }

    public static String forIdentifierString() {
        return forIdentifier().toString();
    }

    public static UUID forRandom() {
        return UUID.randomUUID();
    }

    public static String forRandomString() {
        return forRandom().toString();
    }

    public static String forIdentifierShortcut() {
        return generateUUIDShortcut(forIdentifier());
    }

    public static String forRandomShortcut() {
        return generateUUIDShortcut(forRandom());
    }

    private static String generateUUIDShortcut(UUID uuid) {
        byte[] bytes = ByteBuffer.allocate(2 * Long.BYTES)
                .putLong(uuid.getMostSignificantBits())
                .putLong(uuid.getLeastSignificantBits())
                .array();
        return StringUtil.bytesToBase64(bytes)
                .replace("=", "")
                .replace("\n", "")
                .replace("\r", "")
                .replace("+", "-")
                .replace("/", "_")
                .trim();
    }

    /// Creates an 13-chars abbriviation from last part (with dash at start): `01940236-9cfe-7230-8808-5045f37ab09e` -> `-5045f37ab09e`
    public static String abbr13(UUID uuid) {
        return abbr(uuid, 13);
    }

    /// Creates an 6-chars abbriviation from last part: `01940236-9cfe-7230-8808-5045f37ab09e` -> `7ab09e`
    public static String abbr6(UUID uuid) {
        return abbr(uuid, 6);
    }

    /// Creates an 4-chars abbriviation from last part: `01940236-9cfe-7230-8808-5045f37ab09e` -> `b09e`
    public static String abbr4(UUID uuid) {
        return abbr(uuid, 4);
    }

    public static String abbr(UUID uuid, int charsCount) {
        return uuid.toString().substring(36 - charsCount);
    }

    public static String forIdentifierWithoutDashes() {
        return forIdentifierString().replace("-", "");
    }
}
