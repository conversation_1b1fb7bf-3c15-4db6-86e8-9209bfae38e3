package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ListToModifiedSubListConverter<S, T> implements Converter<List<S>, List<? extends T>> {

    @NonNull Converter<S, T> singleItemConverter;
    @NonFinal boolean allowNullTargetValues = false;
    @NonFinal boolean allowNullSourceValues = false;
    @NonFinal boolean preserveTargetNullValues = false;

    public ListToModifiedSubListConverter<S, T> allowNullSourceValues() {
        return this.allowNullSourceValues(true);
    }

    public ListToModifiedSubListConverter<S, T> allowNullSourceValues(boolean allowNullSourceValues) {
        this.allowNullSourceValues = allowNullSourceValues;
        return this;
    }

    public ListToModifiedSubListConverter<S, T> allowNullTargetValues() {
        return this.allowNullTargetValues(true);
    }

    public ListToModifiedSubListConverter<S, T> allowNullTargetValues(boolean allowNullTargetValues) {
        this.allowNullTargetValues = allowNullTargetValues;
        return this;
    }

    public ListToModifiedSubListConverter<S, T> preserveNullValues(boolean preserveNullValues) {
        this.preserveTargetNullValues = preserveNullValues;
        return this;
    }

    @Override
    public List<? extends T> convert(@NonNull List<S> source) {
        return ListUtil.convert(source, singleItemConverter, allowNullSourceValues, preserveTargetNullValues, allowNullTargetValues);
    }

}
