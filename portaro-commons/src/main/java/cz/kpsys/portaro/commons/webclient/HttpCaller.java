package cz.kpsys.portaro.commons.webclient;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.net.http.HttpResponse;

public interface HttpCaller {
    static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.configure(DeserializationFeature.FAIL_ON_TRAILING_TOKENS, true);
        return mapper;
    }

    HttpCaller withOAuthClientCredentialsAuth(String username, String password);

    HttpCaller withBasicAuth(String username, String password);

    HttpResponse<JsonNode> getFor<PERSON>son(String path);

    HttpResponse<JsonNode> postForJson(String path, String body);

    HttpResponse<JsonNode> deleteForJson(String path);

    HttpCaller withBaseUrl(String baseUrl);

    HttpCaller withAuthorization(java.util.function.Consumer<java.net.http.HttpRequest.Builder> authorization);
}
