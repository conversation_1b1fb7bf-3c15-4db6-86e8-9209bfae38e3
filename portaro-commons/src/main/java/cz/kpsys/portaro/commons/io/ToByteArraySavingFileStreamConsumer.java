package cz.kpsys.portaro.commons.io;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ToByteArraySavingFileStreamConsumer implements FileStreamConsumer {

    private static final long LARGE_FILE_LIMIT = 1024 * 1024 * 1024;

    @NonNull ToByteArraySavingWriteStreamProvider writeStreamProvider = new ToByteArraySavingWriteStreamProvider();
    @NonFinal @NonNull ReadFinishDataCallback readFinishDataCallback = data -> {};
    @NonFinal @NonNull ReadFinishFileCallback readFinishFileCallback = (filename, size, data) -> {};

    public static ToByteArraySavingFileStreamConsumer ofReadFinishCallback(ReadFinishDataCallback callback) {
        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();
        reader.readFinishDataCallback = callback;
        return reader;
    }

    public ToByteArraySavingFileStreamConsumer ofReadFinishCallback(ReadFinishFileCallback callback) {
        ToByteArraySavingFileStreamConsumer reader = new ToByteArraySavingFileStreamConsumer();
        reader.readFinishFileCallback = callback;
        return reader;
    }

    public String getFilename() {
        return writeStreamProvider.getFilename();
    }

    public long getSize() {
        return writeStreamProvider.getSize();
    }

    public byte[] getData() {
        return writeStreamProvider.getData();
    }

    @Override
    public void consume(@NonNull StreamInfo info, @NonNull InputStream inputStream) {
        Long size = info.size();
        writeStreamProvider.setFilename(info.filename());

        OutputStream writeStream = writeStreamProvider.getStream();
        try {
            if (size != null && size < LARGE_FILE_LIMIT) {
                IOUtils.copy(inputStream, writeStream);
            } else {
                IOUtils.copyLarge(inputStream, writeStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Error while copying %s".formatted(info.filename()), e);
        } finally {
            StreamUtils.quietCloseAll(inputStream, writeStream);
        }

        readFinishDataCallback.onFinished(writeStreamProvider.getData());
        readFinishFileCallback.onFinished(writeStreamProvider.getFilename(), writeStreamProvider.getSize(), writeStreamProvider.getData());
    }

    public interface ReadFinishDataCallback {
        void onFinished(byte[] data);
    }

    public interface ReadFinishFileCallback {
        void onFinished(String filename, long size, byte[] data);
    }
}
