package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FilteredAllValuesProvider<E> implements AllValuesProvider<E> {

    @NonNull AllValuesProvider<E> allValuesProvider;
    @NonNull Predicate<E> filter;

    @Override
    public List<E> getAll() {
        return ListUtil.filter(allValuesProvider.getAll(), filter);
    }
}
