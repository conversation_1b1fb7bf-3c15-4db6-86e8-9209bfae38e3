package cz.kpsys.portaro.commons.barcode;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;

public class InvalidBarCodeException extends RuntimeException implements UserFriendlyException, SeveritedException {

    public InvalidBarCodeException(String barCode) {
        super("Invalid Barcode: %s".formatted(barCode));
    }

    @Override
    public Text getText() {
        return Texts.ofMessageCoded("validation.InvalidBarCode");
    }

    @Override
    public int getSeverity() {
        return SEVERITY_WARNING;
    }
}
