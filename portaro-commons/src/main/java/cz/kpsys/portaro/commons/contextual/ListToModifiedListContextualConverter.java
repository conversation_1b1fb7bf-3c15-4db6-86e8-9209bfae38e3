package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ListToModifiedListContextualConverter<S, CTX, T> implements ContextualFunction<List<? extends S>, CTX, List<T>> {

    @NonNull ContextualFunction<S, CTX, T> singleItemConverter;

    @Override
    public List<T> getOn(List<? extends S> input, CTX ctx) {
        return ListUtil.convert(input, source -> singleItemConverter.getOn(source, ctx), false, false, false);
    }
}
