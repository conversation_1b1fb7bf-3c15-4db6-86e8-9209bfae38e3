package cz.kpsys.portaro.test.e2e.web.pages;

import cz.kpsys.portaro.test.e2e.web.utils.PageUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class RegistrationModal {

    final PageUtils pageUtils;

    @FindBy(css = "button[data-qa='registration-option-full-registration-button']")
    WebElement fullRegistrationButton;

    @FindBy(name = "newPassword-repetition")
    WebElement newPasswordRepetitionInput;

    @FindBy(name = "newPassword")
    WebElement newPasswordInput;

    @FindBy(name = "username")
    WebElement userNameInput;

    @FindBy(name = "lastName")
    WebElement lastNameInput;

    @FindBy(name = "firstName")
    WebElement firstNameInput;

    @FindBy(css = "button[data-qa='universal-form-ok-button']")
    WebElement universalFormOkButton;

    @FindBy(css = ".emails input")
    WebElement emailInput;

    @FindBy(css = ".emails .add")
    WebElement addEmailButton;

    @FindBy(css = ".phoneNumbers input[data-main-input]")
    WebElement phoneNumberInput;

    @FindBy(css = ".phoneNumbers .add")
    WebElement addPhoneNumberButton;

    @FindBy(css = ".registrationExpirationDate input")
    WebElement registrationExpirationDateInput;


    public RegistrationModal(@NonNull WebDriver driver) {
        pageUtils = new PageUtils(driver);
        PageFactory.initElements(driver, this);
    }

    public RegistrationModal clickOnFullRegistrationButton() {
        pageUtils.waitForSteadinessAndClick(fullRegistrationButton);
        pageUtils.waitForSteadinessOfElement(universalFormOkButton);
        return this;
    }

    public RegistrationModal enterNewPasswordRepetition(String newPasswordRepetition) {
        pageUtils.waitForSteadinessAndSendKeys(newPasswordRepetitionInput, newPasswordRepetition);
        return this;
    }

    public RegistrationModal enterNewPassword(String newPassword) {
        newPasswordInput.sendKeys(newPassword);
        return this;
    }

    public RegistrationModal enterUserName(String userName) {
        userNameInput.sendKeys(userName);
        return this;
    }

    public RegistrationModal enterLastName(String lastName) {
        lastNameInput.sendKeys(lastName);
        return this;
    }

    public RegistrationModal enterFirstName(String firstName) {
        pageUtils.waitForElementAndSendKeys(firstNameInput, firstName);
        return this;
    }

    public RegistrationModal enterEmail(String email) {
        pageUtils.waitForSteadinessAndClick(addEmailButton);
        emailInput.sendKeys(email);
        return this;
    }

    public RegistrationModal enterPhoneNumber(String number) {
        addPhoneNumberButton.click();
        phoneNumberInput.sendKeys(number);
        return this;
    }

    public RegistrationModal enterRegistrationExpirationDate(String number) {
        registrationExpirationDateInput.clear();
        registrationExpirationDateInput.sendKeys(number);
        return this;
    }

    public RegistrationModal clickOnUniversalFormOkButton() {
        pageUtils.waitForSteadinessAndClick(universalFormOkButton);
        return this;
    }
}
