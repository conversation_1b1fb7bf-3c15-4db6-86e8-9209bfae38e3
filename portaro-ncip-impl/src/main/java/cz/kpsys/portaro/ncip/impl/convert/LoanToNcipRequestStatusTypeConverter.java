package cz.kpsys.portaro.ncip.impl.convert;

import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.ncip.schema.custom.RequestStatusType;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class LoanToNcipRequestStatusTypeConverter implements Converter<Loan, RequestStatusType> {

    @Override
    public RequestStatusType convert(@NonNull Loan loan) {
        String requestStatusTypeValue = getRequestStatusType(loan);
        if (requestStatusTypeValue == null) {
            return null;
        }
        RequestStatusType rst = new RequestStatusType();
        rst.setValue(requestStatusTypeValue);
        return rst;
    }

    private String getRequestStatusType(@NonNull Loan loan) {
        return switch (loan.getState()) {
            case SENT_RESERVATION, PROCESSED_ORDER, PICKUP_READY_SEEKED_FOREIGN_EXEMPLAR -> RequestStatusType.AVAILABLE_FOR_PICKUP;
            case LENT -> null;
            default -> RequestStatusType.IN_PROCESS;
        };
    }

}
