package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.Sip2RenewAllRequest;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2RenewAllRequestSerializer implements TypedSip2MessageSerializer<Sip2RenewAllRequest> {

    @NonNull TypedSip2ValueSerializer<Instant> instantSerializer;

    public static Sip2RenewAllRequestSerializer createDefault() {
        return new Sip2RenewAllRequestSerializer(
                new Sip2InstantSerializer()
        );
    }

    @Override
    public void serialize(@NonNull Sip2RenewAllRequest request, @NonNull CommandSerializing ser) {
        ser.commandIdentifier(request.getType().getIdentifier());

        instantSerializer.serialize(request.getTransactionDate(), ser.header());

        ser.field(AO_INSTITUTION_ID).string(request.getInstitutionId());
        ser.field(AA_PATRON_IDENTIFIER).string(request.getPatronIdentifier());
        ser.field(AD_PATRON_PASSWORD).whenNotNull(request.getPatronPassword()).string(request.getPatronPassword());
        ser.field(AC_TERMINAL_PASSWORD).whenNotNull(request.getTerminalPassword()).string(request.getTerminalPassword());
        ser.field(BO_FEE_ACKNOWLEDGED).charNYOrNoopByThroolean(request.getFeeAcknowledged());
    }
}
