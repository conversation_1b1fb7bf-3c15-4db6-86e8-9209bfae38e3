package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.sip2.client.deserialize.Sip2ResponseParser;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronEnableResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Sip2PatronEnableResponseParser implements Sip2ResponseParser<Sip2PatronEnableResponse> {

    @NonNull PatronStatusParser patronStatusParser;
    @NonNull LanguageParser languageParser;

    public static Sip2PatronEnableResponseParser createDefault() {
        return new Sip2PatronEnableResponseParser(
                new PatronStatusParser(),
                new LanguageParser()
        );
    }

    @Override
    public Sip2PatronEnableResponse parse(RequiredSip2ValueParsing parsing) throws InvalidSip2ResponseValueException {
        Sip2PatronEnableResponse response = new Sip2PatronEnableResponse();

        response.setStatus(patronStatusParser.parse(parsing));
        response.setLanguage(languageParser.getLanguage(parsing.from(16).first3Chars().numericStringValue()));
        response.setTransactionDate(parsing.from(19).dateTimeInstant());

        RequiredSip2ValueParsing fieldsParsing = parsing.from(37);

        response.setInstitutionId(fieldsParsing.field(AO_INSTITUTION_ID).stringValue());
        response.setPatronIdentifier(fieldsParsing.field(AA_PATRON_IDENTIFIER).stringValue());
        response.setPersonalName(fieldsParsing.field(AE_PERSONAL_NAME).stringValue());

        response.setValidPatron(fieldsParsing.field(BL_VALID_PATRON).optional().asNYMissingThroolean());
        response.setValidPatronPassword(fieldsParsing.field(CQ_VALID_PATRON_PASSWORD).optional().asNYMissingThroolean());

        response.setScreenMessage(fieldsParsing.fieldValues(AF_SCREEN_MESSAGE));
        response.setPrintLine(fieldsParsing.fieldValues(AG_PRINT_LINE));

        fieldsParsing.sequence().ifPresent(response::setSequence);

        return response;
    }
}
