package cz.kpsys.portaro.sip2.client.model;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public enum MediaType {

    OTH<PERSON>,
    BOOK,
    <PERSON><PERSON><PERSON>IN<PERSON>,
    BOUND_JOURNAL,
    AUDIO_TAPE,
    VIDEO_TAPE,
    CD_CDROM,
    DISKETTE,
    BOOK_WITH_DISKETTE,
    BOOK_WITH_CD,
    BOOK_WITH_AUDIO_TAPE;
}
