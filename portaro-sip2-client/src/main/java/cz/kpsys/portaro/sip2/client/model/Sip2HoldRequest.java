package cz.kpsys.portaro.sip2.client.model;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.sip2.CommandType;
import cz.kpsys.portaro.sip2.client.Sip2MessageRequest;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.time.Instant;

@Getter
@Setter
public class Sip2HoldRequest extends Sip2MessageRequest<Sip2HoldResponse> {

    /**
     * The type of the hold transaction: add, delete or modify. The
     * default is add.
     */
    @NonNull
    private HoldMode holdMode;

    /**
     * Date and time of the request.
     */
    @NonNull
    private Instant transactionDate;

    /**
     * The expiration date of the hold.
     */
    @NullableNotBlank
    private String expirationDate;

    /**
     * The pickup location of the hold.
     */
    @NullableNotBlank
    private String pickupLocation;

    /**
     * The type of the hold: other, any copy, specific copy
     * or any copy at a single branch or sublocation.
     */
    @Nullable
    private HoldType holdType;

    /**
     * Library's institution id.
     */
    @NonNull
    private String institutionId = "";

    /**
     * An identifying value for the patron, library card's barcode number for example.
     */
    @NonNull
    private String patronIdentifier;

    /**
     * Password (PIN) of the patron. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String patronPassword;

    /**
     * Identifying value for the item.
     */
    @NullableNotBlank
    private String itemIdentifier;

    /**
     * Identifying value for the title.
     */
    @NullableNotBlank
    private String titleIdentifier;

    /**
     * Password for the system to login to the ILS. If this feature is not used by the ILS in the library then the value should be empty if it's required in the request, and can be omitted entirely if the field is optional in the message.
     */
    @NullableNotBlank
    private String terminalPassword;

    /**
     * If false and there's a fee associated with the transaction, the ILS SIP server should tell the system in the response that there's a fee, and refuse to complete the transaction. If the system and the patron then interact and the patron agrees to pay the fee, this field will be set to true on a second request message, indicating to the ILS SIP server that the patron has acknowledged the fee and the transaction should not be refused just because there is a fee associated with it.
     */
    private Throolean feeAcknowledged = Throolean.UNKNOWN;

    /**
     * Bibliographic id of the record. This is a Voyager ESIP extension.
     */
    private String bibId;

    public Sip2HoldRequest(@NonNull Instant transactionDate, @NonNull String patronId) {
        super(CommandType.HOLD_REQUEST);
        this.transactionDate = transactionDate;
        this.holdMode = HoldMode.ADD;
        this.patronIdentifier = patronId;
    }

    public Sip2HoldRequest(@NonNull Instant transactionDate, @NonNull String patronId, String itemId) {
        this(transactionDate, patronId);
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
    }

    public Sip2HoldRequest(@NonNull Instant transactionDate, @NonNull String patronId, String itemId, String titleId) {
        this(transactionDate, patronId);
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
        this.titleIdentifier = titleId;
    }

    public Sip2HoldRequest(@NonNull Instant transactionDate, @NonNull String institutionId, @NonNull String patronId, String itemId, String titleId) {
        this(transactionDate, patronId);
        this.institutionId = institutionId;
        this.patronIdentifier = patronId;
        this.itemIdentifier = itemId;
        this.titleIdentifier = titleId;
    }

}
