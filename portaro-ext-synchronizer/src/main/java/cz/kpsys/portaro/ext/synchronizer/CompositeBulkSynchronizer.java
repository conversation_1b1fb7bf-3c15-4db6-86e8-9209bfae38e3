package cz.kpsys.portaro.ext.synchronizer;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.ext.synchronizer.result.BulkSyncResult;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CompositeBulkSynchronizer<E> implements BulkSynchronizer<E> {

    @NonNull Collection<BulkSynchronizer<E>> concreteBulkSynchronizers;

    @Override
    public BulkSyncResult<E> synchronizeAll() {
        log.info("Scheduled foreign system synchronization started.");
        List<BulkSyncResult<E>> results = ListUtil.convert(concreteBulkSynchronizers, BulkSynchronizer::synchronizeAll);
        results.forEach(report -> log.info(report.toString()));
        return BulkSyncResult.ofMerged(results);
    }
}
