package cz.kpsys.portaro.ext.synchronizer.filters;

import cz.kpsys.portaro.user.edit.command.PersonEditationCommand;
import cz.kpsys.portaro.user.edit.command.UserRecordEditationCommand;
import lombok.NonNull;

import java.util.Optional;

public interface ForeignSystemUserFilter {

    Optional<UserRecordEditationCommand<PersonEditationCommand>> filter(@NonNull UserRecordEditationCommand<PersonEditationCommand> editationRequest);

}
