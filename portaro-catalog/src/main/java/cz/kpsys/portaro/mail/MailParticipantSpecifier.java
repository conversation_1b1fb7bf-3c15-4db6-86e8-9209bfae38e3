package cz.kpsys.portaro.mail;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

@Validated
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MailParticipantSpecifier {

    @NonNull ContextualProvider<Department, @NonNull Locale> defaultLocaleProvider;
    @NonNull UserLocaleResolver userLocaleResolver;
    @NonNull Translator<Department> translator;
    @NonNull ContextualProvider<Department, @NonNull String> systemEmailProvider;
    @NonNull ContextualProvider<Department, @NonNull String> lendingAdminEmail;

    public SpecificMailSendCommand<Department> resolve(@NonNull @Valid NonspecificMailSendCommand command) {
        return new SpecificMailSendCommand<>(
                command.topic(),
                resolveSender(command.sender(), command.department()),
                resolveRecipients(command.recipients(), command.department()),
                localize(command.subject(), command.department(), resolveLocale(command.recipients().stream().findFirst().get(), command.department())),
                command.body(),
                command.attachments(),
                command.department(),
                command.currentAuth()
        );
    }

    private String localize(@NonNull Text text, @NonNull Department department, @Nullable Locale nullableLocale) {
        Locale locale = Optional.ofNullable(nullableLocale)
                .orElseGet(() -> defaultLocaleProvider.getOn(department));
        return text.localize(translator, department, locale);
    }

    private Locale resolveLocale(To recipient, Department ctx) {
        return switch (recipient.type()) {
            case CUSTOM -> resolveLocale(recipient.locale(), ctx);
            case SYSTEM, LENDING_ADMIN -> defaultLocaleProvider.getOn(ctx);
            case OF_USER -> userLocaleResolver.resolveLocale(Objects.requireNonNull(recipient.user()), ctx);
        };
    }

    private Locale resolveLocale(@Nullable Locale locale, @NonNull Department fallbackDepartment) {
        if (locale != null) {
            return locale;
        }
        return defaultLocaleProvider.getOn(fallbackDepartment);
    }

    private SpecificEmailParticipant resolveSender(From sender, Department ctx) {
        return switch (sender.type()) {
            case INTERNAL -> SpecificEmailParticipant.internal(sender.existingInternalUser());
            case CUSTOM -> SpecificEmailParticipant.external(sender.existingEmail(), sender.name());
            case SYSTEM -> SpecificEmailParticipant.external(systemEmailProvider.getOn(ctx), null);
        };
    }

    private Set<SpecificEmailParticipant> resolveRecipients(Collection<To> recipients, Department ctx) {
        return recipients.stream()
                .map(recipient -> resolveRecipient(recipient, ctx))
                .collect(Collectors.toUnmodifiableSet());
    }

    private SpecificEmailParticipant resolveRecipient(To recipient, Department ctx) {
        return switch (recipient.type()) {
            case OF_USER -> SpecificEmailParticipant.internal(recipient.existingUser());
            case CUSTOM -> SpecificEmailParticipant.external(recipient.existingEmail(), null);
            case SYSTEM -> SpecificEmailParticipant.external(systemEmailProvider.getOn(ctx), null);
            case LENDING_ADMIN -> SpecificEmailParticipant.external(lendingAdminEmail.getOn(ctx), null);
        };
    }

}
