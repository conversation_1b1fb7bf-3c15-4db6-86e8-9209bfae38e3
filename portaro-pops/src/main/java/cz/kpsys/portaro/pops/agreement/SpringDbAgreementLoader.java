package cz.kpsys.portaro.pops.agreement;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.file.LoadedIdentifiedFile;
import cz.kpsys.portaro.record.RecordDescriptor;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.Nullable;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.TC;
import static cz.kpsys.portaro.commons.object.repo.DataUtils.requireSingle;
import static cz.kpsys.portaro.database.DbUtils.instantNotNull;
import static cz.kpsys.portaro.database.DbUtils.instantOrNull;
import static cz.kpsys.portaro.pops.PopsConstants.AGREEMENT_TYPE_BUTTON;
import static cz.kpsys.portaro.pops.PopsConstants.AGREEMENT_TYPE_FILE;
import static cz.kpsys.portaro.pops.PopsDb.Agreement.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbAgreementLoader implements AgreementLoader, RowMapper<Agreement> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ByIdLoadable<LoadedIdentifiedFile, Long> loadedIdentifiedFileLoader;

    @Override
    public Agreement getById(@NonNull String id) throws ItemNotFoundException {
        return requireSingle(getBy(id, null, null), Agreement.class, id);
    }

    @Override
    public Optional<Agreement> getBySupplierAndTender(RecordDescriptor supplierAuthority, int tenderId) {
        return getBy(null, supplierAuthority, tenderId).stream().findFirst();
    }

    public List<Agreement> getBy(@Nullable String id, @Nullable RecordDescriptor supplierAuthority, @Nullable Integer tenderId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(POPS_POTVRZENI);

        sq.where().isNull(TC(POPS_POTVRZENI, DELETE_DATE));

        if (id != null) {
            sq.where().and().eq(ID_POTVRZENI, id);
        }

        if (supplierAuthority != null) {
            sq.where().and().eq(TC(POPS_POTVRZENI, SUPPLIER_RECORD_ID), supplierAuthority.getId());
        }

        if (tenderId != null) {
            sq.where().and().eq(TC(POPS_POTVRZENI, FK_RIZENI), tenderId);
        }

        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public Agreement mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        @NonNull String id = DbUtils.getStringNotNull(rs, ID_POTVRZENI);
        @NonNull Integer tenderId = DbUtils.getIntegerNotNull(rs, FK_RIZENI);
        @NonNull UUID supplierRecordId = DbUtils.uuidNotNull(rs, SUPPLIER_RECORD_ID);
        @NonNull Instant createDate = instantNotNull(rs, CREATE_DATE);
        Instant confirmDate = instantOrNull(rs, CONFIRM_DATE);
        Instant deleteDate = instantOrNull(rs, DELETE_DATE);
        @NonNull String type = DbUtils.getStringNotNull(rs, TYPE);

        if (type.equals(AGREEMENT_TYPE_BUTTON)) {
            return new ButtonAgreement(id, tenderId, supplierRecordId, createDate, confirmDate, deleteDate);
        }

        if (type.equals(AGREEMENT_TYPE_FILE)) {
            LoadedIdentifiedFile agreementFile = loadedIdentifiedFileLoader.getById(DbUtils.getLong(rs, FK_FULLTEXT_AGREEMENT));
            LoadedIdentifiedFile signedAgreementFile = Optional.ofNullable(DbUtils.getLong(rs, FK_FULLTEXT_SIGNED_AGREEMENT))
                    .map(loadedIdentifiedFileLoader::getById)
                    .orElse(null);
            return new LoadedSignedFileAgreement(id, tenderId, supplierRecordId, createDate, confirmDate, deleteDate, agreementFile, signedAgreementFile);
        }

        throw new UnsupportedOperationException("Agreement type %s is not supported".formatted(type));
    }
}
