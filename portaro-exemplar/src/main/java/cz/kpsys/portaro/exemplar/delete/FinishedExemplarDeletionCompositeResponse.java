package cz.kpsys.portaro.exemplar.delete;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.List;

public record FinishedExemplarDeletionCompositeResponse(

        @NotEmpty
        List<ActionResponse> exemplarDeletionResponses

) implements ExemplarDeletionCompositeResponse {

    @NonNull
    @Override
    public String responseType() {
        return "FinishedExemplarDeletionCompositeResponse";
    }

    @NonNull
    @Override
    public Text text() {
        return Texts.ofMessageCoded("exemplar.ExemplarSmazan");
    }
}
