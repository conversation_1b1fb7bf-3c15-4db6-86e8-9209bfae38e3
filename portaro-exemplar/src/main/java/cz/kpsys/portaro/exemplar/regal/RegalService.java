package cz.kpsys.portaro.exemplar.regal;

import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.exemplar.Exemplar;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.EmptyResultDataAccessException;

import java.io.Serializable;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RegalService implements Serializable {

    @NonNull ByIdLoadable<RegalMap, String> regalMapLoader;
    @NonNull Provider<String> propertyProMapuRegaluProvider;

    public Optional<RegalMap> getExemplarRegal(Exemplar e) {
        LabeledIdentified<?> labeledValueMapy = (LabeledIdentified<?>) ObjectUtil.getFieldValue(e, propertyProMapuRegaluProvider.get());
        if (labeledValueMapy == null || labeledValueMapy.getId() == null) {
            return Optional.empty();
        }
        String mapIdenfifier = String.valueOf(labeledValueMapy.getId());

        try {
            return Optional.of(regalMapLoader.getById(mapIdenfifier));
        } catch (EmptyResultDataAccessException | ItemNotFoundException ex) {
            return Optional.empty();
        }
    }
    
    public String getPropertyProMapuRegalu() {
        return propertyProMapuRegaluProvider.get();
    }
    
}
