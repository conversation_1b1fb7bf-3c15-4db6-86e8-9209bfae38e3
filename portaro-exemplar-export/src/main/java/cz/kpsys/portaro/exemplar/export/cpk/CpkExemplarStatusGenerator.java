package cz.kpsys.portaro.exemplar.export.cpk;

import cz.kpsys.portaro.exemplar.Exemplar;

import java.util.function.Function;

import static cz.kpsys.portaro.exemplar.exemplarstatus.ExemplarStatus.*;

/**
 * status jednotky
 *  <br/>
 *  <br/> - AR archivní exemplář - budeme vracet pro ty, co nejdou pujcit a zaroven nespadaji do ZP nebo Z
 *  <br/> - P pouze prezenčně
 *  <br/> - A absenčně
 *  <br/> - Z ztráta
 *  <br/> - ZP ve zpracování
 *  <br/> - V vyřazeno - neresime
 *  <br/> - P<PERSON> poškozeno - neresime
 *  <br/> - RZ rezervni exemplář - neresime
 */
public class CpkExemplarStatusGenerator implements Function<Exemplar, String> {

    @Override
    public String apply(Exemplar exemplar) {
        switch (exemplar.getStatus().getId()) {
            case PROCESSING_STATUS_ID:
            case ACQUISITION_STATUS_ID:
            case NAME_DEFINITION_STATUS_ID:
            case FACTUAL_DEFINITION_STATUS_ID:
            case CHECK_STATUS_ID:
            case BINDING_STATUS_ID:
                // ve zpracování
                return "ZP";

            case TEMPORARY_LOST_STATUS_ID:
            case DISCARDED_STATUS_ID:
                // ztráta
                return "Z";
        }

        if (exemplar.getDiscardionId() != null) {
            // vyrazeno
            return "V";
        }

        if (!exemplar.getLoanCategory().isLendable()) {
            // nejde pujcovat -> je to "archivni"
            return "AR";
        }

        if (exemplar.getLoanCategory().isOnSite()) {
            // pouze prezenčně
            return "P";
        }

        return "A";
    }

}
