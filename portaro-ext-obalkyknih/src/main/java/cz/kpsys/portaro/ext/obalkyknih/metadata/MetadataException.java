package cz.kpsys.portaro.ext.obalkyknih.metadata;

import cz.kpsys.portaro.record.Record;
import lombok.experimental.StandardException;

import java.net.URI;

@StandardException
public class MetadataException extends RuntimeException {

    public MetadataException(Record record, URI url, Throwable e) {
        this(String.format("Error while fetching metadata for %s, url: %s", record, url), e);
    }

}
