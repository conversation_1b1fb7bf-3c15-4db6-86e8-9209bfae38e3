Lokalni vyvojove prostedi
=========================

Prerekvizity:
-------------
1.  <PERSON><PERSON><PERSON><PERSON><PERSON> `docker`
1.  <PERSON><PERSON><PERSON><PERSON><PERSON> `docker-compose`
1.  <PERSON><PERSON><PERSON><PERSON>ny `git`
1.  <PERSON><PERSON><PERSON><PERSON>ny `npm`
1.  <PERSON>ny port `8080`
1.  <PERSON><PERSON><PERSON> `portaro`
1.  <PERSON><PERSON><PERSON> `10GB` volneho mista na databazi s aplikacem

Postup:
-------

1.  Vyt<PERSON>it hlavni slozku (napr. `portaro`)

1.  Nako<PERSON><PERSON><PERSON> tam `docker-compose.yml` a vytvořit prázdný adresář `appserver-workspace`

1.  Nakopirovat tam databazi jako `kpsys_develop_2_0.fdb`

    1.  Ze serveru `scp stefan@193.168.1.144:/data/db/kpsys_develop_2_0.fbk.xz <portaro_stack>`
    1.  Rozbalit -> `kpsys_develop_2_0.fbk` (`unxz -kv --threads=0 ./kpsys_develop_2_0.fbk.xz`)
    1.  <PERSON><PERSON><PERSON>vat (zmenit `<portaro_stack>` za ABSOLUTNÍ cestu k adresari a zmenit `<PASSWORD>` za heslo) 
        ```bash
            docker run \
            -v <portaro_stack>:/db \
            kpsys/firebird \
            /usr/local/firebird/bin/gbak -r -v -y /db/restore.log -p 16384 -user SQLKPWIN -password <PASSWORD> /db/kpsys_develop_2_0.fbk /db/kpsys_develop_2_0.fdb
        ```
    1.  Nejakou dobu to pobezi    
    1.  Zkontrolovat `restore.log`, pokud tam nejsou nejake chyby ve stylu:
        ```
            ...
            
            gbak:cannot commit index FK_KATAUT_4_VIZ
            gbak: ERROR:violation of FOREIGN KEY constraint "FK_KATAUT_4_VIZ" on table "KATAUT_4"
            gbak: ERROR:    Foreign key reference target does not exist
            gbak: ERROR:    Problematic kay value is ("FK_AUT" = 300137)
            
            ...
            
            gbak:Database is not online due to failure to activate one ot more indices.
            gbak:Run gfix -online to bring database online without active indicies.
        ```
        1.  Pokud tam jsou chyby, spustit 
        ```bash
            docker run \
            -v <portaro_stack>:/db \
            kpsys/firebird \
            /usr/local/firebird/bin/gfix -online -user SQLKPWIN -password <PASSWORD> /db/kpsys_develop_2_0.fdb

        ```
        
1.  Spustit `docker-compose up -d`

1.  Pokud je vse v poradku, melo by to  nabehnout.
    Stav je mozne sledovat pomoci `docker-compose logs -f`
    
1.  Pri spousteni portara z prostredi IntelliJ IDEA vytvorit novou `Run Configuration`.
    Vybrat `Gradle`
    Nasledne v dialogu vyplnit:
    - `Gradle project:`  `portaro:portaro-runner`
    - `Tasks:` `bootRun`
    - `Environment variables:`
        - `SERVER_PORT=8080` - volitelne, pro spusteni portara na jinem, nez portu 80
        - `APPSERVER_URL=http://***********:8182` - volitelne, pro napojeni na aplikacni server, ktery je jinde, nez na http://localhost:8182
        - `DATABASE_HOST=localhost` - nutne, pro napojeni na databazi jinde, nez na domene "firebird"
    - `Arguments:` `--build-cache --parallel`
        
1.  Pokud je vse spravne, melo by na `localhost` nabehnout portaro.

1. Pro spouštění NPM skriptů z IDE je dobré mít nastavenou proměnnou prostředí s cestou ke Chromiu (Chromu).

    To se dělá tak, že v dialogu `Run/Debug Cofigurations` -> `Templates` -> `npm` nastavíme do `Environment` následující: `CHROMIUM_BIN=<cesta/ke/spustitelnému/souboru>`.
    Tím zajistíme to, že pokaždé, když spustím NPM skript z nabídky nebo vytvořím novou NPM konfiguraci, tato proměnná se nastaví a poběží správně FE testy.

Build
=====

Jar publishing
--------------
Pro publikovani na ftp (ftp://portaro.kpsys.cz/portaro) je treba nastavit prihlasovaci udaje a to budto

- V konfiguraku gradle (~/.gradle/gradle.properties)
  ```
  ftpUsername=<username> 
  ftpPassword=<password>
  ```
- Nebo pres VM argumenty:
  ```
  -PftpUsername=<username> -PftpPassword=<password>
  ```


Maven publishing
----------------
Pro publikovani do maven repo (https://repo-maven.kpsys.cz) je treba nastavit prihlasovaci udaje a to budto

- V konfiguraku gradle (~/.gradle/gradle.properties)
  ```
  mavenUsername=<username> 
  mavenPassword=<password>
  ```
- Nebo pres VM argumenty:
  ```
  -PmavenUsername=<username> -PmavenPassword=<password>
  ```

Architektura aplikace
=====================

## Frontend

Frontend je MPA + SPA webová aplikace psaná v typescriptu. 
Backend slouží zároveň jako webserver pro hostění zbundované klientské části (JS + CSS + Assety).
Část je napsaná v Angular.js a část v Svelte.js.
Postupně refaktorujeme komponenty z angularu do svelte.

[Frontend README](portaro-runner/src/main/resources/resources/README.md)

## Popis cesty requestu od prohlížeče na backend a zpět

Dejme tomu, že chce javascriptová část aplikace (frontend) uložit komentář knížky.
Na to by na backendu měl existovat endpoint (url adresa) `/api/comments`, jenž je definován v `CommentApiControlleru`.
Daný endpoint příjímá objekt typu `CommentRequest`, který se automaticky naplní z JSONu v requestu. Může mít jak jednoduché 
fieldy (např. `Instant createDate`), tak i komplexní (např. `BasicUser author`, i když v JSONu bude pouze id - `"author": 1234`), 
které se automaticky načtou z databáze podle id - díky tomu, že je na backendu nakonfigurován automatický konverter `Integer` -> `BasicUser`.

V metodě endpointu se následně přemapuje `CommentRequest` na `Comment` (což už je objekt servisní vrstvy) a zavolá se `commentSaver.save(comment)`.
Objekt `commentSaver` je typu `Saver<Comment>`. Jestli se ukládá do databáze, do paměti, nebo někam do cloudu nás v kontroleru nezajímá, 
je to implementační detail. Zajímá nás to pouze v konfiguraci aplikace, která je v samostatném modulu a je ve třídě `CommentBeansConfig`.
V ní je nastavená implementace, která typicky
1. přemapuje `Comment` na `CommentEntity`, což je objekt reprezentující řádek v databázi
1. uloží `CommentEntity` pomocí obecného hibernate saveru
1. může smazat cache, pokud se `CommentEntity` cachuje

Po úspěšném uložení se vrátí objekt typu `ActionResponse`, typicky `FinishedSaveResponse`, který se automaticky přemapuje do JSONu.

## Základní architektura backendu

Portaro využívá několik základních rozhraní, které se vyskytují napříč celým backendem. 
Princip je většinou stejný - máme objekt (entitu), např. výpůjčku `Loan`, která je uložená v DB, má tedy id a může mít třeba i název. 
Tu budeme chtít načíst z databáze podle id, nebo budeme chtít načíst hromadně několik výpůjček najednou pomocí seznamu idček, 
vyhledat podle jejich parametrů nebo uložit do DB. Výpůjčku, seznam výpůjček nebo hledání budeme chtít zobrazit na frontendu, 
takže ji vystavíme v nějakém `LoanApiController`, ze kterého výpůjčka se automaticky serializuje do JSONu.

Entity většinou implementují (extendují):
- `Identified<ID>` reprezentuje jakýkoliv objekt, který má id. většinou pochází z DB. Např. `Loan` nebo `Location`.
  Nejpoužívanější implementace, které se používají jako rodičovské třídy, jsou `BasicIdentified`.
- `NamedIdentified<ID>` je `Identified<ID>`, který má navíc i název. Např. `Loan` nebo `Location`.
  Nejpoužívanější implementace, které se používají jako rodičovské třídy, jsou `BasicNamedIdentified`.

Máme-li entitu typu `Identified<ID>`, budeme ji pravděpodobně chtít
- Načíst (např z DB) podle ID metodou `getById` v rozraní `ByIdLoadable<E, ID>`, 
které je třeba naimplementovat a zaregistrovat do Springu jako `@Bean`.
- Načíst hromadně podle seznamu IDček metodou `getAllByIds` v rozraní `AllByIdsLoadable<E, ID>`.
- Vyhledat podle nějakých parametrů `MapBackedParams` metodou `getContent` v rozhraní `ParameterizedSearchLoader`.
- Vyhledávat i na frontendu podle nějakých parametrů `MapBackedParams` metodou `getContent` v rozhraní `ParameterizedSearchLoader`.


## Příklad vytvoření nové entity

V naprosté většine úkolů, pokud budeme chtít vytvořit novou entitu, budeme potřebovat zaimplementovat následující 
(příklad s entitou reprezentující komentář knížky uživatelem)

1. **Vytvořit datovou entitu (DTO)** `CommentEntity`, 
reprezentující řádek v DB v tabulce komentářů.
Jednotlivé fieldy jsou jednoduché datové typy, tzn. nebude obsahovat field `User author`, ale `@NonNull Integer authorId`.
Používáme Lombok, takže pokud fieldy chceme vždy nenullové, nastavujeme `@NonNull User author`. U nullovatelných nastavujeme `@Nullable` buďto z javax nebo ze springu.

1. **Vytvořit model tridy (Service Layer)** `Comment`,
která reprezentuje model komentáře.
Jednotlivé fieldy již mohou být komplexní typy, tzn. zde již bude `@NonNull User author`.

1. **Vytvořit mapper** pro konverzi z entity (DTO) na model `modelsFromEntityConverter`, 
což je třída implementující rozhraní `Converter<List<CommentEntity>, List<Comment>>`, 
která mapuje najednou celý seznam entit (DTOček) na modely. Celý seznam se mapuje kvůli rychlosti, protože se zde obvykle 
načítají komplexní typy, takže chceme načíst autory všech komentářů najednou (pomocí `BatchLoader`).

1. **Vytvořit mapper** pro konverzi z model na entitu (DTO) `modelToEntityConverter`, 
což je třída implementující rozhraní `Converter<Comment, CommentEntity>`, 
která mapuje model na entitu (DTOčko). Zde nikoliv senž seznam, ale jednotlivě - zde se obvykle již žádné komplexní typy nenačítají.

1. **Vytvořit loader** pro načítání entit `commentLoader`

1. **Vytvořit searcher** pro vyhledávání entit `commentSearchLoader`

1. **Vytvořit api endpoint** (kontroler) `CommentApiController`

1. **Zaregistrovat id-to-model konverter**

1. **Nastavit práva**


## Hledání

Portaro umožňuje implementovat hledání jakékoliv entity, která je typu `Identified<ID>` a 
používá na to univerzální systém, do kterého pro hledání nové entity stačí zaregistrovat novou implementaci.

Hledání používá jednotný entrypoint na url `/api/search`, kde se pomocí url parametrů definuje jednak 
- co se hledá, např dokument, uživatel nebo výpůjčka, pomocí parametru `kind` a druhak
- filtry, např `isbn` nebo `username`

Pro různé typy entit, které se hledají se může použít jiné hledání 
(např. pro hledání dokumentů se hledá přes lucene, pro hledání uživatelů a výpůjček se hledá v databázi, 
pokaždé však v jiné tabulce). Taková hledání jsou vytvářena rozhraním `SearchFactory`, např. `loanSearchFactory`.
Na základě jakýchkoliv parametrů se pomocí `SearchFactoryResolver` vybere ta správná searchFactory. V drtivé většině se 
vybírá na základě hodnoty parametru `kind`. Takže pokud `kind=loan`, vybere se `loanSearchFactory`.

Daná searchFactory pak opět na základě parametrů vytváří už konkrétní hledání, 
což je instance jednoho vyhledání podle danách parametrů. Jedná se o implementace rozhraní `Search` a 
pokud jde o jednoduché vyhledání v databázové tabulce, většinou se jedná o `PageSearchLoaderSearch`. 

#### Příklad

K zaintegrování hledání nové entity, např. `Loan`, je třeba 
- implementovat rozhraní `SearchFactory`, např `loanSearchFactory` jako beanu do 
`SearchImplBeansConfig`
  - v naprosté většině to znamená vytvořit beanu `ParameterizedSearchLoaderImpl`, 
    např `PageSearchLoader<MapBackedParams, Loan>`, což jsou funkce pro vyhledání záznamů a 
    jejich počtu podle daných parametrů např. v databázi 
  - vracení `PageSearchLoaderSearch` s nějakými přednastavenými defaultními parametry
- `loanSearchFactory` zaregistrovat v beaně `SearchViewBeansConfig.searchFactoryResolver`.


## Práva

Práva jsou v Portaru řešena pomocí `cz.kpsys.portaro.security.SecurityManager`, který má několik metod říkajících, 
jestli může provést danou akci (`cz.kpsys.portaro.security.Action<SUBJECT>`) aktuálně přihlášený uživatel (`cz.kpsys.portaro.auth.UserAuthentication`)
na aktuálním oddělení (`cz.kpsys.portaro.department.Department`) a volitelně objekt akce (`SUBJECT`). 
Akce může být bez subjektu (např. `Action<Void> LOAN_CATEGORIES_SHOW` - zobrazení kategorií výpůjček), 
nebo se subjektem (např. `Action<LoanCategory> LOAN_CATEGORY_SHOW` - zobrazení konkrétní kategorie).

Definice těchto práv jsou uloženy v mapě v `cz.kpsys.portaro.security.PermissionRegistry` jako jednotlivé implementace
`cz.kpsys.portaro.security.PermissionResolver`.

#### Příklad

K zabezpečení smazání exempláře v metodě kontroleru ExemplarApiController je třeba
- Vytvořit akci - konstantu `Action<Exemplar> EXEMPLAR_DELETE` v ExemplarSecurityActions
- Naimplementovat a zaregistrovat logiku povolení/zakázání akce do `PermissionRegistry` 
v `cz.kpsys.portaro.config.ExemplarBeansConfig.registerPermissions`
- Zavolání `securityManager.throwIfCannot(ExemplarSecurityActions.EXEMPLAR_DELETE, currentAuth, currentDepartment, exemplar);` 
v ExemplarApiController
