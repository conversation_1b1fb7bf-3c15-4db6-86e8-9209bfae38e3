package cz.kpsys.portaro.view.web.rest;

import cz.kpsys.portaro.centralindex.ForeignDocumentAvailabilityService;
import cz.kpsys.portaro.centralindex.ForeignOccurrence;
import cz.kpsys.portaro.centralindex.ForeignOccurrenceLoader;
import cz.kpsys.portaro.loan.availability.PlainAvailability;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ForeignOccurrenceApiController extends GenericApiController {

    @NonNull ForeignOccurrenceLoader foreignOccurrenceLoader;
    @NonNull ForeignDocumentAvailabilityService foreignDocumentAvailabilityService;

    @GetMapping("/api/foreign-occurrences")
    @RateLimited("getForeignDocumentOccurrences")
    public List<ForeignOccurrence> getAllByDocument(@RequestParam("record") Record record) {
        return foreignOccurrenceLoader.getAllByRecord(record);
    }

    @RequestMapping("/api/foreign-occurrences/{id}/foreign-availability")
    @RateLimited("getForeignDocumentAvailability")
    public PlainAvailability getForeignDocumentAvailability(@PathVariable("id") ForeignOccurrence foreignOccurrence) {
        return foreignDocumentAvailabilityService.getDocumentAvailability(foreignOccurrence);
    }

}