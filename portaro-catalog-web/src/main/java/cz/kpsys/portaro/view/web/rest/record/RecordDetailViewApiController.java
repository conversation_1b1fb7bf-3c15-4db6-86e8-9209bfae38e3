package cz.kpsys.portaro.view.web.rest.record;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.record.document.AuthoritySourceDocumentProvider;
import cz.kpsys.portaro.record.view.GenericRecordDetailViewFactory;
import cz.kpsys.portaro.record.view.RecordShowListener;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import cz.kpsys.portaro.web.server.ServerUrl;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;
import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@Hidden
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.RECORDS_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordDetailViewApiController extends GenericApiController {

    @NonNull ByIdLoadable<@NonNull Record, String> recordStringIdPrefixDispatchingLoader;
    @NonNull GenericRecordDetailViewFactory genericRecordDetailViewFactory;
    @NonNull RecordShowListener documentShowListener;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull AuthoritySourceDocumentProvider authoritySourceDocumentProvider;


    @GetMapping("{id}/view")
    @RateLimited("getRecordPage")
    public Object show(@PathVariable("id") String id,
                             @CurrentDepartment Department ctx,
                             @ServerUrl String serverUrl,
                             UserAuthentication currentAuth,
                             Locale locale) {

        Record record = recordStringIdPrefixDispatchingLoader.getById(id);

        securityManager.throwIfCannot(RecordSecurityActions.RECORD_SHOW, currentAuth, ctx, record);

        if (!record.getType().equals(TYPE_DOCUMENT) && !record.getType().equals(TYPE_AUTHORITY)) {
            throw new IllegalStateException("Unknown type of record %s".formatted(record));
        }

        if (record.getType().equals(TYPE_DOCUMENT)) {
            documentShowListener.recordShowed(record);
        }

        Optional<UUID> sourceDocumentId = Optional.empty();

        if (record.getType().equals(TYPE_AUTHORITY)) {
            sourceDocumentId = authoritySourceDocumentProvider.getAuthoritySourceDocumentId(record);
        }

        return genericRecordDetailViewFactory.createView(record, currentAuth, ctx, serverUrl, locale, sourceDocumentId);
    }
}
