package cz.kpsys.portaro.auth.saml2.idp.auth;

import cz.kpsys.portaro.auth.saml2.idp.attribute.Saml2AttributeName;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import net.shibboleth.utilities.java.support.security.impl.RandomIdentifierGenerationStrategy;
import org.opensaml.saml.common.SAMLVersion;
import org.opensaml.saml.saml2.core.Issuer;
import org.opensaml.saml.saml2.core.Response;
import org.opensaml.saml.saml2.core.Status;
import org.opensaml.saml.saml2.core.StatusCode;
import org.opensaml.saml.saml2.core.impl.IssuerBuilder;
import org.opensaml.saml.saml2.core.impl.ResponseBuilder;
import org.opensaml.saml.saml2.core.impl.StatusBuilder;
import org.opensaml.saml.saml2.core.impl.StatusCodeBuilder;
import org.springframework.security.saml2.core.OpenSamlInitializationService;

import java.time.Instant;
import java.util.Set;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Saml2ResponseCreator {

    static {
        OpenSamlInitializationService.initialize();
    }

    @NonNull Saml2AssertionFactory saml2AssertionFactory;
    @NonNull ContextualProvider<Department, @NonNull String> idpEntityIdProvider;

    @NonNull
    public Response createResponse(@NonNull Saml2AuthRequest authRequest,
                                   @NonNull BasicUser targetUser,
                                   @NonNull Set<Saml2AttributeName> attributesToProvide) {
        Instant authenticationTime = authRequest.getAuthenticationInstant();

        Response response = new ResponseBuilder().buildObject();
        response.setID(new RandomIdentifierGenerationStrategy().generateIdentifier());
        response.setIssueInstant(authenticationTime);
        response.setVersion(SAMLVersion.VERSION_20);
        response.setIssuer(buildIssuer(authRequest));
        response.setDestination(authRequest.getAssertionConsumerServiceURL());
        response.setStatus(buildStatus());
        response.getAssertions().add(saml2AssertionFactory.buildAssertion(authRequest, authenticationTime, targetUser, attributesToProvide));

        return response;
    }

    private Issuer buildIssuer(Saml2AuthRequest authRequest) {
        Issuer issuer = new IssuerBuilder().buildObject();
        issuer.setValue(idpEntityIdProvider.getOn(authRequest.getDepartment()));
        return issuer;
    }

    private Status buildStatus() {
        StatusCode statusCode = new StatusCodeBuilder().buildObject();
        statusCode.setValue(StatusCode.SUCCESS);
        Status status = new StatusBuilder().buildObject();
        status.setStatusCode(statusCode);
        return status;
    }
}
