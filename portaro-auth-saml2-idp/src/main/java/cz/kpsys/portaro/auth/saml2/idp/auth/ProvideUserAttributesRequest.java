package cz.kpsys.portaro.auth.saml2.idp.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.saml2.idp.attribute.Saml2AttributeName;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.api.ActionRequestMethod;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.multipleacceptable.MultipleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmableRequest;
import cz.kpsys.portaro.formconfig.valueeditor.AcceptableValuesResolver;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.BeforeIntegrityValidation;
import cz.kpsys.portaro.formannotation.annotations.validation.IntegrityValidation;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

import java.util.List;

@Form(id = "provideUserAttributesRequest",
        title = "Potvrzení předávaných údajů",
        modifierBean = "provideUserAttributesRequestFormModifier"
)
@FormSubmit(path = Saml2AuthRequestController.SAML2_AUTH_ENDPOINT, method = ActionRequestMethod.GET, asPage = true)
@BeforeIntegrityValidation(bean = "provideUserAttributesRequestPreValidationModifier")
@ProvideUserAttributesRequestRequiredAttributesFilled
@With
@FieldNameConstants
public record ProvideUserAttributesRequest(

        Boolean confirmed,

        @JsonProperty(Saml2AuthRequestController.SAML_REQUEST_PARAMETER_NAME)
        @NotBlank(groups = IntegrityValidation.class)
        String base64SamlRequest,

        @JsonProperty(Saml2AuthRequestController.RELAY_STATE_PARAMETER_NAME)
        @Nullable
        String relayState,

        @JsonProperty(Saml2AuthRequestController.SIG_ALG_PARAMETER_NAME)
        @Nullable
        String signatureAlgorithm,

        @JsonProperty(Saml2AuthRequestController.SIGNATURE_PARAMETER_NAME)
        @Nullable
        String signature,

        @FormPropertyLabel("Předávané údaje")
        @MultipleAcceptableEditor(valuesSourceBean = "provideUserAttributesFormAttributesResolver", switchToInlineModeThreshold = Integer.MAX_VALUE, visibleIfSingleValue = true)
        @NotEmpty(groups = IntegrityValidation.class)
        List<Saml2AttributeName> attributes

) implements ConfirmableRequest<ProvideUserAttributesRequest> {

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class ProvideUserAttributesRequestPreValidationModifier implements TypedAuthenticatedContextualObjectModifier<ProvideUserAttributesRequest> {

        @NonNull AcceptableValuesResolver<ProvideUserAttributesRequest, Saml2AttributeName> provideUserAttributesRequiredAttributesResolver;
        @NonNull ContextualProvider<Department, @NonNull Boolean> attributesConfirmationEnabledProvider;

        @Override
        public ProvideUserAttributesRequest modify(ProvideUserAttributesRequest formObject, Department ctx, UserAuthentication currentAuth) {
            if (formObject.attributes() == null) {
                formObject = formObject.withAttributes(provideUserAttributesRequiredAttributesResolver.resolveAcceptableValues(formObject, ctx));
            }
            if (formObject.confirmed() == null && attributesConfirmationEnabledProvider.getOn(ctx)) {
                formObject = formObject.withConfirmed(false);
            }
            return formObject;
        }
    }
}
