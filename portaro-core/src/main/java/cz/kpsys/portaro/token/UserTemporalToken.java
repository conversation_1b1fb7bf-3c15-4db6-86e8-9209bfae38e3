package cz.kpsys.portaro.token;

import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

import java.time.Duration;
import java.time.Instant;

public record UserTemporalToken(

        @NonNull
        BasicUser user,

        @NonNull
        BasicTemporalToken basicTemporalToken

) implements TemporalToken {

    public static UserTemporalToken ofSingleScope(@NonNull BasicUser user,
                                                  @NonNull String scope,
                                                  @NonNull Duration durationFromNow) {
        return of(user, Scope.parse(scope), durationFromNow);
    }

    public static UserTemporalToken of(@NonNull BasicUser user,
                                       @NonNull Scope scopes,
                                       @NonNull Duration durationFromNow) {
        return of(user, BasicTemporalToken.of(scopes, durationFromNow));
    }

    public static UserTemporalToken of(@NonNull BasicUser user,
                                       @NonNull BasicTemporalToken basicTemporalToken) {
        return new UserTemporalToken(user, basicTemporalToken);
    }

    @NonNull
    @Override
    public Scope scopes() {
        return basicTemporalToken.scopes();
    }

    @NonNull
    @Override
    public Instant expirationDate() {
        return basicTemporalToken.expirationDate();
    }

    @Override
    public UserTemporalToken assertScope(@NonNull String scope) throws InsufficientScopeException {
        TemporalToken.super.assertScope(scope);
        return this;
    }
}
