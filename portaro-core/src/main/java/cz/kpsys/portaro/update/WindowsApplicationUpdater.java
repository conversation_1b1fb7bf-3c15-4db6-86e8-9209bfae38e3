package cz.kpsys.portaro.update;

import cz.kpsys.portaro.shutdown.ApplicationRestarter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class WindowsApplicationUpdater implements ApplicationUpdater {

    @NonNull ApplicationRestarter applicationRestarter;

    @Override
    public void update() {
        applicationRestarter.restart();
    }

}
