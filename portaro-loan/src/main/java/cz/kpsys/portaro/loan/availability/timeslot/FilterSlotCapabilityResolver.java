package cz.kpsys.portaro.loan.availability.timeslot;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.filter.Filter;
import cz.kpsys.portaro.filter.FilterResult;
import cz.kpsys.portaro.loan.availability.resolver.CapabilityResolverResult;
import cz.kpsys.portaro.loan.availability.resolver.RichCapabilityResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FilterSlotCapabilityResolver implements RichCapabilityResolver<NotUseredSlottedCapabilityResolveCommand> {

    @NonNull Filter<SlottedCapabilityResolveCommand> filter;

    @Override
    public @NonNull CapabilityResolverResult resolve(@NonNull NotUseredSlottedCapabilityResolveCommand command) {
        FilterResult filterResult = filter.doFilter(command);
        if (filterResult.isDenied()) {
            String reasonCode = filterResult.getCode().orElseGet(SlotNonCapableReason.UNKNOWN::getId);
            Text reasonText = filterResult.getText().orElseGet(SlotNonCapableReason.UNKNOWN::getText);
            return CapabilityResolverResult.ofNotCapable(reasonCode, reasonText);
        }
        return CapabilityResolverResult.ofCapable();
    }

    @Override
    public String toString() {
        return "FilterSlotCapabilityResolver{filter=%s}".formatted(filter);
    }
}
