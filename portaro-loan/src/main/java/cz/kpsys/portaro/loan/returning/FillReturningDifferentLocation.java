package cz.kpsys.portaro.loan.returning;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.business.command.CommandInterceptor;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FillReturningDifferentLocation implements CommandInterceptor<LoanReturnCommand> {

    @NonNull ContextualProvider<Department, @NonNull Boolean> checkDifferentLocation;

    @Override
    public LoanReturnCommand process(@NonNull LoanReturnCommand command) {
        Boolean ignoring = command.ignoreDocumentIsFromDifferentLocation();
        if (ignoring == null && !checkDifferentLocation.getOn(command.department())) {
            // Je vypnutý check defaultní hodnoty, ignoruje se
            return command.withIgnoreDocumentIsFromDifferentLocation(true);
        }
        return command;
    }

}
