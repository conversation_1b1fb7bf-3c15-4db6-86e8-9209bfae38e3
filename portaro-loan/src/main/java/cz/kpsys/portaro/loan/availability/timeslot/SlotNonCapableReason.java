package cz.kpsys.portaro.loan.availability.timeslot;

import com.fasterxml.jackson.annotation.JsonFormat;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Getter
public enum SlotNonCapableReason implements LabeledIdentified<String> {

    SLOT_IN_PAST("slot-in-past", Texts.ofMessageCoded("loan.nonCapableReason.Previous")),
    WORKING_DAY_SLOT_ON_NOT_WORKING_DAY("slot-not-on-working-day", Texts.ofMessageCoded("loan.nonCapableReason.NonWorkingDay")),
    OCCUPIED("occupied", Texts.ofMessageCoded("loan.nonCapableReason.Occupied")),
    MISSING_REQUESTER_USER("missing-requester-user", Texts.ofMessageCoded("loan.nonCapableReason.MissingApplicant")),
    NO_EXEMPLAR_WITHIN_SCOPE("no-exemplar-within-scope", Texts.ofMessageCoded("loan.nonCapableReason.NoExemplarWithinScope")),
    UNKNOWN("unknown", Texts.ofMessageCoded("loan.nonCapableReason.UnknownReason"));

    public static final Codebook<SlotNonCapableReason, String> CODEBOOK = new StaticCodebook<>(values());

    @NonNull String id;
    @NonNull Text text;

}
