package cz.kpsys.portaro.loan.availability;

import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.AS;
import static cz.kpsys.portaro.commons.db.QueryUtils.MAX;
import static cz.kpsys.portaro.databasestructure.LoanDb.VYPUC.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbReservationsCountLoader implements ReservationsCountLoader {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;


    @Override
    public int getReservationsInQueueCount(UUID recordId) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(AS(MAX(CIS_REZE), "pocet_rezervaci"));
        sq.from(TABLE_ACTIVE);
        sq.where()
                .eq(RECORD_ID, recordId)
                .and()
                .gtEq(CIS_REZE, Loan.CIS_REZE_REZE_MIN)
                .and()
                .ltEq(CIS_REZE, Loan.CIS_REZE_REZE_MAX);
        try {
            return DbUtils.intResult(jdbcTemplate, sq);
        } catch (ItemNotFoundException | EmptyResultDataAccessException e) {
            return 0;
        }
    }

}
