package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.commons.validation.uppercase.Uppercase;
import cz.kpsys.portaro.department.Department;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;

import static cz.kpsys.portaro.user.BasicUser.createNewUserId;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@FieldNameConstants
public class Library extends Institution {

    public static final int SIGLA_MAX_LENGTH = 121;

    @Deprecated
    final String kind = "LIBRARY";

    @Setter
    @NullableNotBlank
    @Uppercase
    String sigla;

    @Nullable
    Department linkedDepartment;

    public Library(@NonNull UserStringGenerator prettyUserNameGenerator, Integer id, @NullableNotBlank String name, @NullableNotBlank String sigla, @Nullable Department linkedDepartment) {
        super(prettyUserNameGenerator, id, name, InstitutionType.LIBRARY);
        this.sigla = sigla;
        this.linkedDepartment = linkedDepartment;
    }

    public static Library createNew(@NonNull UserStringGenerator prettyUserNameGenerator) {
        return new Library(prettyUserNameGenerator, createNewUserId(), null, null, null);
    }

}
