package cz.kpsys.portaro.file.custom;

import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.ProtocolResolver;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.util.Optional;

import static cz.kpsys.portaro.CoreConstants.Resource.CUSTOM_FILE_PREFIX;

/**
 * Supports location e.g. "custom:/html/index.html" <br/>
 * Adds default root department to location (-> "custom:/1/html/index.html") and delegates to withDepartmentResolver
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomFileResourceWithoutDepartmentPrefixProtocolResolver implements ProtocolResolver {

    @NonNull Provider<Department> currentDepartmentProvider;
    @NonNull CustomFileResourceWithDepartmentPrefixProtocolResolver withDepartmentResolver;

    public CustomFileResourceWithoutDepartmentPrefixProtocolResolver(@NonNull ByIdLoadable<CustomFile, String> customFileLoader, @NonNull FileDataStreamer fileDataStreamer, @NonNull Provider<Department> currentDepartmentProvider) {
        this.currentDepartmentProvider = currentDepartmentProvider;
        this.withDepartmentResolver = new CustomFileResourceWithDepartmentPrefixProtocolResolver(customFileLoader, fileDataStreamer);
    }

    @Override
    public Resource resolve(@NonNull String location, @NonNull ResourceLoader resourceLoader) {
        Optional<String> directoryAndFilenameOpt = StringUtil.removePrefixOpt(location, CUSTOM_FILE_PREFIX);
        if (directoryAndFilenameOpt.isEmpty()) {
            return null;
        }
        String customFileId = CustomFile.createId(currentDepartmentProvider.get().getId(), directoryAndFilenameOpt.get());
        String departmentedLocation = prefix(customFileId);
        return withDepartmentResolver.resolve(departmentedLocation, resourceLoader);
    }

    private String prefix(String location) {
        return CUSTOM_FILE_PREFIX + location;
    }

}
