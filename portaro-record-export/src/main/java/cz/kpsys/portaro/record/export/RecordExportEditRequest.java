package cz.kpsys.portaro.record.export;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ConfirmableRequest;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.validation.AfterIntegrityValidationViolation;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.bool.BooleanEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.list.ListEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.multipleacceptable.MultipleAcceptableEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.text.TextEditor;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

@Form(id = "recordExportEdit", title = "Export")
@FormSubmit(path = "/api/record-exports/edit")
@AfterIntegrityValidationViolation(bean = "recordExportRequestDefaulter")
@With
public record RecordExportEditRequest(

        Boolean confirmed,

        @NotNull
        UUID id,

        @FormPropertyLabel("{export.request.oaiSetId}")
        @TextEditor
        @NotBlank
        String oaiSetId,

        @FormPropertyLabel("{export.request.oaiSetName}")
        @TextEditor
        @NotBlank
        String oaiSetName,

        @FormPropertyLabel("Record Operation Type")
        @MultipleAcceptableEditor(valuesSourceBean = "recordOperationTypeLoader", emptyAsNull = true)
        List<RecordOperationType> recordOperationTypes,

        @FormPropertyLabel("{commons.Fondy}")
        @MultipleAcceptableEditor(valuesSourceBean = "enabledFondsProvider", emptyAsNull = true)
        List<Fond> fonds,

        @FormPropertyLabel("Allowed states")
        @MultipleAcceptableEditor(valuesSourceBean = "allowedRecordStatusesProvider", emptyAsNull = true)
        List<RecordStatus> recordStatuses,

        @FormPropertyLabel("{commons.Departments}")
        @MultipleAcceptableEditor(valuesSourceBean = "departmentLoader", emptyAsNull = true)
        List<Department> rootDepartments,

        @FormPropertyLabel("withPerio")
        @BooleanEditor
        Boolean withPerio,

        @FormPropertyLabel("withNonperio")
        @BooleanEditor
        Boolean withNonperio,

        @FormPropertyLabel("withNonexemplared")
        @BooleanEditor
        Boolean withNonexemplared,

        @FormPropertyLabel("forbiddenRecordIds")
        @ListEditor
        List<@TextEditor UUID> forbiddenRecordIds,

        @Nullable
        @NullableNotBlank
        String sqlClause

) implements ConfirmableRequest<RecordExportEditRequest>, RecordExportRequest {}
